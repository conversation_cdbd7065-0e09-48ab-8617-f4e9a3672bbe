---
alwaysApply: true
---

# Flow Reference Wallet Cursor Rules

## 1. Project Structure & Layering

- **src/background/**: Background scripts, Chrome API, message routing.
  - **Can import from:** [core](mdc:src/core/), [shared](mdc:packages/shared/)
  - **Must NOT import from:** [ui](mdc:src/ui/)
- **src/core/**: Business logic, blockchain, wallet services.
  - **Can import from:** [shared](mdc:packages/shared/), [background/webapi](mdc:src/background/webapi/)
  - **Must NOT import from:** [ui](mdc:src/ui/), other background code (except webapi)
- **src/ui/**: All UI code (React).
  - **Can import from:** [shared](mdc:packages/shared/), workspace packages
  - **Must NOT import from:** [core](mdc:src/core/), [background](mdc:src/background/)
  - **Internal structure:**
    - **components/**: Reusable UI components.
      - **Can import from:** hooks, utils, assets, style
      - **Must NOT import from:** views
    - **views/**: Page-level components/screens.
      - **Can import from:** components, hooks, utils, assets, style
    - **hooks/**: Custom React hooks.
      - **Can import from:** utils, reducers, shared
      - **Must NOT import from:** views, components
      - **Only hooks may initiate background messaging.**
    - **utils/**: UI-specific utilities.
      - **Must NOT import from:** other UI subfolders (no cross-imports)
    - **assets/**: Static assets (images, icons, fonts, SVGs).
    - **style/**: Theming, CSS, design tokens.
- **src/content-script/**: Web page integration, dApp provider injection.
  - **Can import from:** [background](mdc:src/background/), [shared](mdc:packages/shared/)
- **packages/shared/**: Pure utilities, types, constants.
  - **Must NOT import from:** any project folders
- **packages/reducers/**: Pure state reducers, data transformations.
  - **Can import from:** [shared](mdc:packages/shared/)
  - **Must NOT import from:** any project folders or other packages
- **ESLint enforces these boundaries. Violations are critical errors.**

## 2. File Naming & Placement

- **React Components**: `kebab-case.tsx` (in `components/` or `FRWComponent/`)
- **Hooks**: `use-kebab-case.ts` (in `hooks/`)
- **Other TS Files**: `kebab-case.ts`
- **No `index.ts` or `index.tsx` files** for re-exporting directories unless required by framework.
- **Shared components**: Place in `src/ui/FRWComponent/` and use PascalCase.

## 3. Package Management

- Always use `pnpm` for all dependency management and scripts.

## 4. Data Caching & State

- Use the custom stale-while-revalidate cache system ([docs/cache-data-model.md](mdc:docs/cache-data-model.md)).
- **All cache data is set/modified only by the background script.**
- **All cache keys must be fully self-contained** (no reliance on global state or external context).
- **Background services must use `registerRefreshListener` or `registerBatchRefreshListener`** for all cacheable data.
- **Frontend must use hooks like `useCachedData` and `useUserData`** for all data access.
- **Batch refresh is required for high-frequency or list data** (e.g., balances, NFT lists).
- UI reads data and requests refreshes via hooks.
- All cache/user data keys must be self-contained and typed.

## 5. Import/Dependency Rules

- Use path aliases for cross-folder imports (e.g., `@/folder/*`, `@onflow/frw-shared/*`).
- Enforce strict layer boundaries (UI ↔ Core/Background separation).
- Shared and reducers packages must remain pure and isolated (no side effects, no project imports).

## 6. Testing & Type Safety

- Use `pnpm test:run` for unit tests ([Vitest](mdc:vitest.config.ts)).
- Use `pnpm test:e2e` for end-to-end tests ([Playwright](mdc:playwright.config.ts)).
- All new code must be covered by unit or e2e tests as appropriate.
- All cache and user data types must be explicitly typed and validated.
- Reducers and shared packages must remain pure and have no side effects.
- Use `pnpm lint`, `pnpm format`, and `pnpm storybook` as appropriate.

## 7. Messaging & Communication

- UI communicates with background **only via messaging**, never direct imports.
- **Hooks are the only place in UI allowed to initiate background communication.**

## 8. Documentation & References

- All new architectural or caching patterns must be documented in the appropriate `docs/` file.
- If in doubt, consult the [Architecture Separation](mdc:docs/architecture-separation.md) and [Cache Data Model](mdc:docs/cache-data-model.md) docs.
- Challenge assumptions and ask for clarification if a rule or practice seems unclear.

---

For more details, see:

- [Architecture Separation](mdc:docs/architecture-separation.md)
- [Cache Data Model](mdc:docs/cache-data-model.md)
- [Cadence Scripts Usage Analysis](mdc:docs/cadence-scripts-usage-analysis.md)
- [React Rules](mdc:docs/react-rules.md)
- [messages.json](mdc:src/messages.json) for i18n
- [firebaseConfig.ts](mdc:src/core/utils/firebaseConfig.ts) for remote config

To add new data to the cache:

1.  **Define Keys**: Add key generation functions and type definitions in `cache-data-keys.ts` or `user-data-keys.ts`.
2.  **Create Background Loader**: In a background service, use `registerRefreshListener` or `registerBatchRefreshListener` to define how data is fetched.
3.  **Use Frontend Hooks**: Access data in UI components with the `useCachedData` or `useUserData` hooks.
4.  **Create Background Loader**: In a background service, use `registerRefreshListener` or `registerBatchRefreshListener` to define how data is fetched.
5.  **Use Frontend Hooks**: Access data in UI components with the `useCachedData` or `useUserData` hooks.

# Flow Reference Wallet Cursor Rules

## 1. Project Structure & Layering

- **src/background/**: Background scripts, Chrome API, message routing.
  - **Can import from:** [core](mdc:src/core/), [shared](mdc:packages/shared/)
  - **Must NOT import from:** [ui](mdc:src/ui/)
- **src/core/**: Business logic, blockchain, wallet services.
  - **Can import from:** [shared](mdc:packages/shared/), [background/webapi](mdc:src/background/webapi/)
  - **Must NOT import from:** [ui](mdc:src/ui/), other background code (except webapi)
- **src/ui/**: All UI code (React).
  - **Can import from:** [shared](mdc:packages/shared/), workspace packages
  - **Must NOT import from:** [core](mdc:src/core/), [background](mdc:src/background/)
  - **Internal structure:**
    - **components/**: Reusable UI components.
      - **Can import from:** hooks, utils, assets, style
      - **Must NOT import from:** views
    - **views/**: Page-level components/screens.
      - **Can import from:** components, hooks, utils, assets, style
    - **hooks/**: Custom React hooks.
      - **Can import from:** utils, reducers, shared
      - **Must NOT import from:** views, components
      - **Only hooks may initiate background messaging.**
    - **utils/**: UI-specific utilities.
      - **Must NOT import from:** other UI subfolders (no cross-imports)
    - **assets/**: Static assets (images, icons, fonts, SVGs).
    - **style/**: Theming, CSS, design tokens.
- **src/content-script/**: Web page integration, dApp provider injection.
  - **Can import from:** [background](mdc:src/background/), [shared](mdc:packages/shared/)
- **packages/shared/**: Pure utilities, types, constants.
  - **Must NOT import from:** any project folders
- **packages/reducers/**: Pure state reducers, data transformations.
  - **Can import from:** [shared](mdc:packages/shared/)
  - **Must NOT import from:** any project folders or other packages
- **ESLint enforces these boundaries. Violations are critical errors.**

## 2. File Naming & Placement

- **React Components**: `kebab-case.tsx` (in `components/` or `FRWComponent/`)
- **Hooks**: `use-kebab-case.ts` (in `hooks/`)
- **Other TS Files**: `kebab-case.ts`
- **No `index.ts` or `index.tsx` files** for re-exporting directories unless required by framework.
- **Shared components**: Place in `src/ui/FRWComponent/` and use PascalCase.

## 3. Package Management

- Always use `pnpm` for all dependency management and scripts.

## 4. Data Caching & State

- Use the custom stale-while-revalidate cache system ([docs/cache-data-model.md](mdc:docs/cache-data-model.md)).
- **All cache data is set/modified only by the background script.**
- **All cache keys must be fully self-contained** (no reliance on global state or external context).
- **Background services must use `registerRefreshListener` or `registerBatchRefreshListener`** for all cacheable data.
- **Frontend must use hooks like `useCachedData` and `useUserData`** for all data access.
- **Batch refresh is required for high-frequency or list data** (e.g., balances, NFT lists).
- UI reads data and requests refreshes via hooks.
- All cache/user data keys must be self-contained and typed.

## 5. Import/Dependency Rules

- Use path aliases for cross-folder imports (e.g., `@/folder/*`, `@onflow/frw-shared/*`).
- Enforce strict layer boundaries (UI ↔ Core/Background separation).
- Shared and reducers packages must remain pure and isolated (no side effects, no project imports).

## 6. Testing & Type Safety

- Use `pnpm test:run` for unit tests ([Vitest](mdc:vitest.config.ts)).
- Use `pnpm test:e2e` for end-to-end tests ([Playwright](mdc:playwright.config.ts)).
- All new code must be covered by unit or e2e tests as appropriate.
- All cache and user data types must be explicitly typed and validated.
- Reducers and shared packages must remain pure and have no side effects.
- Use `pnpm lint`, `pnpm format`, and `pnpm storybook` as appropriate.

## 7. Messaging & Communication

- UI communicates with background **only via messaging**, never direct imports.
- **Hooks are the only place in UI allowed to initiate background communication.**

## 8. Documentation & References

- All new architectural or caching patterns must be documented in the appropriate `docs/` file.
- If in doubt, consult the [Architecture Separation](mdc:docs/architecture-separation.md) and [Cache Data Model](mdc:docs/cache-data-model.md) docs.
- Challenge assumptions and ask for clarification if a rule or practice seems unclear.

---

For more details, see:

- [Architecture Separation](mdc:docs/architecture-separation.md)
- [Cache Data Model](mdc:docs/cache-data-model.md)
- [Cadence Scripts Usage Analysis](mdc:docs/cadence-scripts-usage-analysis.md)
- [React Rules](mdc:docs/react-rules.md)
- [messages.json](mdc:src/messages.json) for i18n
- [firebaseConfig.ts](mdc:src/core/utils/firebaseConfig.ts) for remote config

To add new data to the cache:

1.  **Define Keys**: Add key generation functions and type definitions in `cache-data-keys.ts` or `user-data-keys.ts`.
2.  **Create Background Loader**: In a background service, use `registerRefreshListener` or `registerBatchRefreshListener` to define how data is fetched.
3.  **Use Frontend Hooks**: Access data in UI components with the `useCachedData` or `useUserData` hooks.

4.  **Create Background Loader**: In a background service, use `registerRefreshListener` or `registerBatchRefreshListener` to define how data is fetched.
5.  **Use Frontend Hooks**: Access data in UI components with the `useCachedData` or `useUserData` hooks.
