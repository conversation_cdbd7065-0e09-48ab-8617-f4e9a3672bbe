import React from 'react';

export const AddressIcon = ({
  color = '#00EF8B',
  width = 29,
  height = 29,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.6123 0.920898H22.8877C25.5656 0.920898 27.75 3.10535 27.75 5.7832V23.0586C27.75 25.7365 25.5656 27.9209 22.8877 27.9209H5.6123C2.93445 27.9209 0.75 25.7365 0.75 23.0586V5.7832C0.75 3.10535 2.93445 0.920898 5.6123 0.920898ZM17.1338 7.11719C16.2697 7.1173 15.584 7.80292 15.584 8.66699V9.9873H12.916V8.66699C12.916 7.80292 12.2303 7.1173 11.3662 7.11719C10.5204 7.11719 9.81641 7.79852 9.81641 8.66699V9.9873H8.49609C7.64595 9.9873 6.94629 10.687 6.94629 11.5371C6.9464 12.4055 7.65037 13.0869 8.49609 13.0869H9.81641V15.7549H8.49609C7.65037 15.7549 6.9464 16.4363 6.94629 17.3047C6.94629 18.1548 7.64595 18.8545 8.49609 18.8545H9.81641V20.1748C9.81641 21.0433 10.5204 21.7246 11.3662 21.7246C12.2303 21.7245 12.916 21.0389 12.916 20.1748V18.8545H15.584V20.1748C15.584 21.0389 16.2697 21.7245 17.1338 21.7246C17.9796 21.7246 18.6836 21.0433 18.6836 20.1748V18.8545H20.0039C20.8724 18.8545 21.5537 18.1505 21.5537 17.3047C21.5536 16.4406 20.868 15.7549 20.0039 15.7549H18.6836V13.0869H20.0039C20.868 13.0869 21.5536 12.4012 21.5537 11.5371C21.5537 10.6913 20.8724 9.9873 20.0039 9.9873H18.6836V8.66699C18.6836 7.79852 17.9796 7.11719 17.1338 7.11719ZM15.584 13.0869V15.7549H12.916V13.0869H15.584Z"
        fill={color}
        stroke={color}
      />
    </svg>
  );
};
