import React from 'react';

export const DevicesIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21 7.1709H19.5V6.4209C19.5 5.82416 19.2629 5.25187 18.841 4.82991C18.419 4.40795 17.8467 4.1709 17.25 4.1709H3.75C3.15326 4.1709 2.58097 4.40795 2.15901 4.82991C1.73705 5.25187 1.5 5.82416 1.5 6.4209V15.4209C1.5 16.0176 1.73705 16.5899 2.15901 17.0119C2.58097 17.4338 3.15326 17.6709 3.75 17.6709H14.25V18.4209C14.25 19.0176 14.4871 19.5899 14.909 20.0119C15.331 20.4338 15.9033 20.6709 16.5 20.6709H21C21.5967 20.6709 22.169 20.4338 22.591 20.0119C23.0129 19.5899 23.25 19.0176 23.25 18.4209V9.4209C23.25 8.82416 23.0129 8.25186 22.591 7.82991C22.169 7.40795 21.5967 7.1709 21 7.1709ZM3.75 16.1709C3.55109 16.1709 3.36032 16.0919 3.21967 15.9512C3.07902 15.8106 3 15.6198 3 15.4209V6.4209C3 6.22199 3.07902 6.03122 3.21967 5.89057C3.36032 5.74992 3.55109 5.6709 3.75 5.6709H17.25C17.4489 5.6709 17.6397 5.74992 17.7803 5.89057C17.921 6.03122 18 6.22199 18 6.4209V7.1709H16.5C15.9033 7.1709 15.331 7.40795 14.909 7.82991C14.4871 8.25186 14.25 8.82416 14.25 9.4209V16.1709H3.75ZM21.75 18.4209C21.75 18.6198 21.671 18.8106 21.5303 18.9512C21.3897 19.0919 21.1989 19.1709 21 19.1709H16.5C16.3011 19.1709 16.1103 19.0919 15.9697 18.9512C15.829 18.8106 15.75 18.6198 15.75 18.4209V9.4209C15.75 9.22199 15.829 9.03122 15.9697 8.89057C16.1103 8.74992 16.3011 8.6709 16.5 8.6709H21C21.1989 8.6709 21.3897 8.74992 21.5303 8.89057C21.671 9.03122 21.75 9.22199 21.75 9.4209V18.4209ZM12.75 19.9209C12.75 20.1198 12.671 20.3106 12.5303 20.4512C12.3897 20.5919 12.1989 20.6709 12 20.6709H8.25C8.05109 20.6709 7.86032 20.5919 7.71967 20.4512C7.57902 20.3106 7.5 20.1198 7.5 19.9209C7.5 19.722 7.57902 19.5312 7.71967 19.3906C7.86032 19.2499 8.05109 19.1709 8.25 19.1709H12C12.1989 19.1709 12.3897 19.2499 12.5303 19.3906C12.671 19.5312 12.75 19.722 12.75 19.9209ZM20.25 10.9209C20.25 11.1198 20.171 11.3106 20.0303 11.4512C19.8897 11.5919 19.6989 11.6709 19.5 11.6709H18C17.8011 11.6709 17.6103 11.5919 17.4697 11.4512C17.329 11.3106 17.25 11.1198 17.25 10.9209C17.25 10.722 17.329 10.5312 17.4697 10.3906C17.6103 10.2499 17.8011 10.1709 18 10.1709H19.5C19.6989 10.1709 19.8897 10.2499 20.0303 10.3906C20.171 10.5312 20.25 10.722 20.25 10.9209Z"
        fill={color}
      />
    </svg>
  );
};
