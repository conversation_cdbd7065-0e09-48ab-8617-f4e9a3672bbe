import React from 'react';

export const BugIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.5 8.7091C13.5 8.4866 13.566 8.26909 13.6896 8.08409C13.8132 7.89908 13.9889 7.75489 14.1945 7.66974C14.4 7.58459 14.6262 7.56231 14.8445 7.60572C15.0627 7.64913 15.2632 7.75627 15.4205 7.91361C15.5778 8.07094 15.685 8.2714 15.7284 8.48963C15.7718 8.70785 15.7495 8.93405 15.6644 9.13962C15.5792 9.34519 15.435 9.52089 15.25 9.64451C15.065 9.76812 14.8475 9.8341 14.625 9.8341C14.3266 9.8341 14.0405 9.71558 13.8295 9.5046C13.6185 9.29362 13.5 9.00747 13.5 8.7091ZM9.375 7.5841C9.1525 7.5841 8.93499 7.65008 8.74998 7.7737C8.56498 7.89732 8.42078 8.07302 8.33564 8.27858C8.25049 8.48415 8.22821 8.71035 8.27162 8.92858C8.31502 9.14681 8.42217 9.34726 8.5795 9.5046C8.73684 9.66193 8.93729 9.76908 9.15552 9.81249C9.37375 9.85589 9.59995 9.83362 9.80552 9.74847C10.0111 9.66332 10.1868 9.51912 10.3104 9.33412C10.434 9.14911 10.5 8.93161 10.5 8.7091C10.5 8.41073 10.3815 8.12459 10.1705 7.91361C9.95952 7.70263 9.67337 7.5841 9.375 7.5841ZM20.25 13.5841C20.2521 14.3121 20.1575 15.0372 19.9688 15.7404L22.0538 16.6516C22.2091 16.7223 22.3356 16.844 22.4125 16.9963C22.4894 17.1487 22.512 17.3228 22.4766 17.4897C22.4412 17.6567 22.3499 17.8066 22.2178 17.9146C22.0857 18.0227 21.9207 18.0825 21.75 18.0841C21.6467 18.0843 21.5445 18.0629 21.45 18.0213L19.4419 17.1466C18.7701 18.5506 17.7148 19.7359 16.398 20.5656C15.0811 21.3953 13.5564 21.8356 12 21.8356C10.4436 21.8356 8.91889 21.3953 7.60205 20.5656C6.28521 19.7359 5.22994 18.5506 4.55813 17.1466L2.55 18.0213C2.45548 18.0629 2.35329 18.0843 2.25 18.0841C2.07778 18.084 1.91084 18.0246 1.77722 17.916C1.64361 17.8073 1.55147 17.6559 1.51628 17.4874C1.48109 17.3188 1.50499 17.1432 1.58397 16.9902C1.66295 16.8371 1.7922 16.7159 1.95 16.6469L4.03125 15.7404C3.84251 15.0372 3.74794 14.3121 3.75 13.5841V12.8341H1.5C1.30109 12.8341 1.11032 12.7551 0.96967 12.6144C0.829018 12.4738 0.75 12.283 0.75 12.0841C0.75 11.8852 0.829018 11.6944 0.96967 11.5538C1.11032 11.4131 1.30109 11.3341 1.5 11.3341H3.75V10.5841C3.74794 9.85607 3.84251 9.13099 4.03125 8.42785L1.95 7.52129C1.8576 7.48336 1.77371 7.42735 1.70325 7.35655C1.63279 7.28575 1.57719 7.2016 1.53969 7.10902C1.5022 7.01644 1.48358 6.9173 1.48492 6.81743C1.48626 6.71756 1.50753 6.61895 1.54749 6.52741C1.58746 6.43587 1.6453 6.35324 1.71763 6.28436C1.78997 6.21548 1.87533 6.16174 1.96871 6.1263C2.06209 6.09085 2.16162 6.07442 2.26144 6.07796C2.36126 6.0815 2.45936 6.10494 2.55 6.14692L4.55813 7.0216C5.22994 5.61763 6.28521 4.43234 7.60205 3.60263C8.91889 2.77292 10.4436 2.33264 12 2.33264C13.5564 2.33264 15.0811 2.77292 16.398 3.60263C17.7148 4.43234 18.7701 5.61763 19.4419 7.0216L21.45 6.1441C21.6314 6.06964 21.8347 6.06916 22.0164 6.14275C22.1981 6.21634 22.3438 6.35816 22.4222 6.53783C22.5006 6.71751 22.5056 6.92075 22.436 7.10404C22.3665 7.28733 22.2279 7.4361 22.05 7.51848L19.9688 8.43254C20.1575 9.13568 20.2521 9.86076 20.25 10.5888V11.3388H22.5C22.6989 11.3388 22.8897 11.4178 23.0303 11.5585C23.171 11.6991 23.25 11.8899 23.25 12.0888C23.25 12.2877 23.171 12.4785 23.0303 12.6191C22.8897 12.7598 22.6989 12.8388 22.5 12.8388H20.25V13.5841ZM5.25 11.3341H18.75V10.5841C18.75 8.79389 18.0388 7.077 16.773 5.81113C15.5071 4.54526 13.7902 3.8341 12 3.8341C10.2098 3.8341 8.4929 4.54526 7.22703 5.81113C5.96116 7.077 5.25 8.79389 5.25 10.5841V11.3341ZM11.25 20.291V12.8341H5.25V13.5841C5.25197 15.2438 5.86464 16.8446 6.9712 18.0816C8.07775 19.3185 9.60081 20.1049 11.25 20.291ZM18.75 13.5841V12.8341H12.75V20.291C14.3992 20.1049 15.9222 19.3185 17.0288 18.0816C18.1354 16.8446 18.748 15.2438 18.75 13.5841Z"
        fill={color}
      />
    </svg>
  );
};
