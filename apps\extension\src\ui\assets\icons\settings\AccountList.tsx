import React from 'react';

export const AccountListIcon = ({
  color = '#00EF8B',
  width = 29,
  height = 29,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask id="path-1-inside-1_3715_32194" fill="white">
        <path d="M24.75 0.420898C26.9591 0.420898 28.75 2.21176 28.75 4.4209V24.4209C28.75 26.63 26.9591 28.4209 24.75 28.4209H4.75C2.54086 28.4209 0.75 26.63 0.75 24.4209V4.4209C0.75 2.21176 2.54086 0.420899 4.75 0.420898H24.75ZM16.2441 12.4209C15.3273 12.4209 14.584 13.1642 14.584 14.0811C14.584 14.9979 15.3273 15.7412 16.2441 15.7412H22.8848C23.8014 15.741 24.5449 14.9977 24.5449 14.0811C24.5449 13.1643 23.8014 12.4211 22.8848 12.4209H16.2441Z" />
      </mask>
      <path
        d="M24.75 0.420898C26.9591 0.420898 28.75 2.21176 28.75 4.4209V24.4209C28.75 26.63 26.9591 28.4209 24.75 28.4209H4.75C2.54086 28.4209 0.75 26.63 0.75 24.4209V4.4209C0.75 2.21176 2.54086 0.420899 4.75 0.420898H24.75ZM16.2441 12.4209C15.3273 12.4209 14.584 13.1642 14.584 14.0811C14.584 14.9979 15.3273 15.7412 16.2441 15.7412H22.8848C23.8014 15.741 24.5449 14.9977 24.5449 14.0811C24.5449 13.1643 23.8014 12.4211 22.8848 12.4209H16.2441Z"
        fill={color}
      />
      <path
        d="M28.75 4.4209H29.75H28.75ZM24.75 28.4209V29.4209V28.4209ZM0.75 24.4209H-0.25H0.75ZM4.75 0.420898V-0.579102V0.420898ZM14.584 14.0811H13.584V14.0811L14.584 14.0811ZM22.8848 15.7412V16.7412H22.885L22.8848 15.7412ZM24.5449 14.0811L25.5449 14.0811V14.0811H24.5449ZM22.8848 12.4209L22.885 11.4209H22.8848V12.4209ZM24.75 0.420898V1.4209C26.4069 1.4209 27.75 2.76404 27.75 4.4209H28.75H29.75C29.75 1.65947 27.5114 -0.579102 24.75 -0.579102V0.420898ZM28.75 4.4209H27.75V24.4209H28.75H29.75V4.4209H28.75ZM28.75 24.4209H27.75C27.75 26.0778 26.4069 27.4209 24.75 27.4209V28.4209V29.4209C27.5114 29.4209 29.75 27.1823 29.75 24.4209H28.75ZM24.75 28.4209V27.4209H4.75V28.4209V29.4209H24.75V28.4209ZM4.75 28.4209V27.4209C3.09315 27.4209 1.75 26.0778 1.75 24.4209H0.75H-0.25C-0.25 27.1823 1.98858 29.4209 4.75 29.4209V28.4209ZM0.75 24.4209H1.75V4.4209H0.75H-0.25V24.4209H0.75ZM0.75 4.4209H1.75C1.75 2.76404 3.09315 1.4209 4.75 1.4209V0.420898V-0.579102C1.98858 -0.579101 -0.25 1.65947 -0.25 4.4209H0.75ZM4.75 0.420898V1.4209H24.75V0.420898V-0.579102H4.75V0.420898ZM16.2441 12.4209V11.4209C14.775 11.4209 13.584 12.6119 13.584 14.0811H14.584H15.584C15.584 13.7165 15.8796 13.4209 16.2441 13.4209V12.4209ZM14.584 14.0811L13.584 14.0811C13.5841 15.5501 14.775 16.7412 16.2441 16.7412V15.7412V14.7412C15.8797 14.7412 15.584 14.4456 15.584 14.081L14.584 14.0811ZM16.2441 15.7412V16.7412H22.8848V15.7412V14.7412H16.2441V15.7412ZM22.8848 15.7412L22.885 16.7412C24.3536 16.7409 25.5449 15.5502 25.5449 14.0811L24.5449 14.0811L23.5449 14.081C23.5449 14.4453 23.2492 14.7411 22.8845 14.7412L22.8848 15.7412ZM24.5449 14.0811H25.5449C25.5449 12.6118 24.3535 11.4212 22.885 11.4209L22.8848 12.4209L22.8845 13.4209C23.2493 13.421 23.5449 13.7169 23.5449 14.0811H24.5449ZM22.8848 12.4209V11.4209H16.2441V12.4209V13.4209H22.8848V12.4209Z"
        fill={color}
        mask="url(#path-1-inside-1_3715_32194)"
      />
    </svg>
  );
};
