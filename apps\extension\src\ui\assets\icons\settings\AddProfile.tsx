import React from 'react';

export const AddProfileIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.7499 5.33411C15.7499 5.1352 15.8289 4.94443 15.9696 4.80378C16.1102 4.66313 16.301 4.58411 16.4999 4.58411H17.9999V3.08411C17.9999 2.8852 18.0789 2.69443 18.2196 2.55378C18.3602 2.41313 18.551 2.33411 18.7499 2.33411C18.9488 2.33411 19.1396 2.41313 19.2802 2.55378C19.4209 2.69443 19.4999 2.8852 19.4999 3.08411V4.58411H20.9999C21.1988 4.58411 21.3896 4.66313 21.5302 4.80378C21.6709 4.94443 21.7499 5.1352 21.7499 5.33411C21.7499 5.53302 21.6709 5.72379 21.5302 5.86444C21.3896 6.00509 21.1988 6.08411 20.9999 6.08411H19.4999V7.58411C19.4999 7.78302 19.4209 7.97379 19.2802 8.11444C19.1396 8.25509 18.9488 8.33411 18.7499 8.33411C18.551 8.33411 18.3602 8.25509 18.2196 8.11444C18.0789 7.97379 17.9999 7.78302 17.9999 7.58411V6.08411H16.4999C16.301 6.08411 16.1102 6.00509 15.9696 5.86444C15.8289 5.72379 15.7499 5.53302 15.7499 5.33411ZM21.6149 10.4604C21.9575 12.4971 21.644 14.5901 20.7196 16.4371C19.7952 18.2841 18.3077 19.7895 16.472 20.7359C14.6362 21.6824 12.5471 22.021 10.5064 21.7028C8.46568 21.3846 6.57887 20.4261 5.11842 18.9656C3.65796 17.5051 2.69943 15.6183 2.38122 13.5776C2.06302 11.5369 2.40159 9.44781 3.34808 7.61204C4.29456 5.77628 5.79996 4.28881 7.64693 3.3644C9.49391 2.43999 11.5869 2.12647 13.6237 2.46911C13.8183 2.50354 13.9916 2.61345 14.1056 2.77493C14.2197 2.93642 14.2654 3.13641 14.2328 3.33141C14.2001 3.5264 14.0918 3.70063 13.9314 3.81619C13.771 3.93175 13.5714 3.9793 13.3762 3.94848C12.1932 3.74948 10.981 3.81065 9.82404 4.12774C8.66708 4.44483 7.59312 5.01023 6.67689 5.78458C5.76066 6.55893 5.02418 7.52363 4.51869 8.61156C4.0132 9.69948 3.75085 10.8845 3.7499 12.0841C3.74813 14.1037 4.49039 16.0531 5.8349 17.56C6.67137 16.3479 7.84754 15.4102 9.21553 14.8647C8.4807 14.286 7.94449 13.4926 7.68147 12.5949C7.41845 11.6973 7.44169 10.74 7.74795 9.85618C8.05422 8.97236 8.62829 8.20594 9.39034 7.66351C10.1524 7.12107 11.0645 6.82959 11.9999 6.82959C12.9353 6.82959 13.8474 7.12107 14.6095 7.66351C15.3715 8.20594 15.9456 8.97236 16.2519 9.85618C16.5581 10.74 16.5814 11.6973 16.3183 12.5949C16.0553 13.4926 15.5191 14.286 14.7843 14.8647C16.1523 15.4102 17.3284 16.3479 18.1649 17.56C19.5094 16.0531 20.2517 14.1037 20.2499 12.0841C20.2499 11.623 20.2117 11.1627 20.1355 10.7079C20.1183 10.6103 20.1205 10.5103 20.1421 10.4136C20.1638 10.3169 20.2043 10.2255 20.2615 10.1445C20.3186 10.0636 20.3913 9.99482 20.4752 9.94212C20.5591 9.88943 20.6526 9.85388 20.7503 9.83753C20.848 9.82118 20.948 9.82436 21.0445 9.84687C21.141 9.86939 21.2321 9.9108 21.3124 9.96871C21.3928 10.0266 21.461 10.0999 21.5129 10.1843C21.5648 10.2687 21.5995 10.3625 21.6149 10.4604ZM11.9999 14.3341C12.5932 14.3341 13.1733 14.1582 13.6666 13.8285C14.16 13.4989 14.5445 13.0303 14.7715 12.4822C14.9986 11.934 15.058 11.3308 14.9423 10.7488C14.8265 10.1669 14.5408 9.63235 14.1212 9.21279C13.7017 8.79323 13.1671 8.50751 12.5852 8.39175C12.0032 8.276 11.4 8.33541 10.8519 8.56247C10.3037 8.78953 9.83514 9.17405 9.50549 9.6674C9.17585 10.1607 8.9999 10.7408 8.9999 11.3341C8.9999 12.1298 9.31597 12.8928 9.87858 13.4554C10.4412 14.018 11.2043 14.3341 11.9999 14.3341ZM11.9999 20.3341C13.8312 20.336 15.6105 19.7255 17.0549 18.5997C16.5123 17.7512 15.7649 17.0529 14.8815 16.5692C13.9981 16.0855 13.0071 15.8319 11.9999 15.8319C10.9927 15.8319 10.0018 16.0855 9.11833 16.5692C8.23491 17.0529 7.48747 17.7512 6.9449 18.5997C8.38931 19.7255 10.1686 20.336 11.9999 20.3341Z"
        fill={color}
      />
    </svg>
  );
};
