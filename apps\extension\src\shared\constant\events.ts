export const EVENTS = {
  walletIntialized: 'walletIntialized',
  broadcastToUI: 'broadcastTo<PERSON>',
  broadcastToBackground: 'broadcastToBackground',
  UIToBackground: 'UIToBackground',
  SIGN_FINISHED: 'SIGN_FINISHED',
  WALLETCONNECT: {
    STATUS_CHANGED: 'WALLETCONNECT_STATUS_CHANGED',
    INIT: 'WALLETCONNECT_INIT',
    INITED: 'WALLETCONNECT_INITED',
  },
  GNOSIS: {
    TX_BUILT: 'TransactionBuilt',
    TX_CONFIRMED: 'TransactionConfirmed',
  },
};
