import React from 'react';

export const WalletConnectIcon = ({
  color = '#00EF8B',
  width = 23,
  height = 15,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 23 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.4873 7.92612L16.333 13.9066L11.9473 9.64291H11.9463C11.8171 9.51696 11.6544 9.46909 11.5146 9.46909C11.4086 9.4691 11.2895 9.49654 11.1816 9.56479L11.082 9.64291L6.69629 13.9056L0.519531 7.92514L1.85449 6.6312L6.24219 10.8958V10.8949C6.37152 11.0215 6.53473 11.0697 6.6748 11.0697C6.81612 11.0696 6.98056 11.0201 7.11035 10.891L11.4922 6.63217L15.8789 10.8958V10.8949C16.0082 11.0215 16.1714 11.0697 16.3115 11.0697C16.4529 11.0696 16.6172 11.0202 16.7471 10.891L21.125 6.63608C21.1327 6.62984 21.1409 6.62827 21.1465 6.62827H21.1484L22.4873 7.92612ZM5.06152 3.52182C8.50485 0.175201 14.0329 0.0708291 17.6064 3.20834L17.9463 3.52182L18.3672 3.93393L17.0312 5.23276L16.5908 4.80405C13.8646 2.1588 9.51434 2.07621 6.68652 4.556L6.41699 4.80405L5.93164 5.27573L4.5957 3.97787L5.06152 3.52182Z"
        fill={color}
        stroke={color}
      />
    </svg>
  );
};
