import React from 'react';

import { COLOR_GREEN_FLOW_DARKMODE_00EF8B } from '@/ui/style/color';

export const FlowIcon = ({
  color = COLOR_GREEN_FLOW_DARKMODE_00EF8B,
  width = 45,
  height = 45,
  showWhiteBackground = false,
}: {
  color?: string;
  width?: number;
  height?: number;
  showWhiteBackground?: boolean;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 45 45"
      fill={color}
      xmlns="http://www.w3.org/2000/svg"
    >
      {showWhiteBackground && <circle cx="22.5" cy="22.5" r="22.5" fill="#FDFBF9" />}
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M45 22.5C45 34.9264 34.9264 45 22.5 45C10.0736 45 0 34.9264 0 22.5C0 10.0736 10.0736 0 22.5 0C34.9264 0 45 10.0736 45 22.5ZM26.0192 18.981L32.3731 18.981V25.335H26.0192V27.7155C26.0192 29.4431 25.5069 31.1318 24.5472 32.5682C23.5874 34.0046 22.2233 35.1241 20.6272 35.7852C19.0312 36.4463 17.275 36.6192 15.5807 36.2822C13.8864 35.9452 12.33 35.1133 11.1085 33.8918C9.88693 32.6702 9.05505 31.1139 8.71803 29.4196C8.38101 27.7252 8.55398 25.969 9.21507 24.373C9.87617 22.777 10.9957 21.4128 12.4321 20.4531C13.8685 19.4933 15.5572 18.981 17.2847 18.981H19.6697V25.3305H17.2847C16.813 25.3305 16.3519 25.4704 15.9597 25.7325C15.5675 25.9946 15.2618 26.367 15.0812 26.8028C14.9007 27.2386 14.8535 27.7182 14.9455 28.1808C15.0376 28.6435 15.2647 29.0684 15.5983 29.402C15.9318 29.7355 16.3568 29.9627 16.8194 30.0547C17.2821 30.1467 17.7616 30.0995 18.1974 29.919C18.6332 29.7385 19.0057 29.4328 19.2678 29.0406C19.5298 28.6484 19.6697 28.1873 19.6697 27.7155V25.3305H26.0191V18.981H19.6697L19.6697 18.1845C19.6721 15.8687 20.5931 13.6485 22.2306 12.011C23.8681 10.3734 26.0884 9.45243 28.4042 9.45005H35.5502V15.804H28.4042C27.7724 15.804 27.1665 16.0547 26.7193 16.501C26.2722 16.9473 26.0204 17.5528 26.0192 18.1845V18.981Z"
        fill={color}
        fillOpacity="1"
      />
    </svg>
  );
};
