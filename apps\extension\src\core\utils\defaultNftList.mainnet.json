{"name": "Flow NFT List", "network": "mainnet", "chainId": 747, "tokens": [{"chainId": 747, "address": "0x30cf5dcf6ea8d379", "contractName": "AeraNFT", "path": {"storage": "/storage/aeraNFTs", "public": "/public/aeraNFTs"}, "evmAddress": "0x5fd25838361b4ae8b705691b4fd19a36e8fc64a2", "flowAddress": "0x30cf5dcf6ea8d379", "name": "Aera", "description": "Aera by OneFootball", "logoURI": "https://miro.medium.com/fit/c/176/176/1*hzfP51MATg3et5vghYy1uQ.png", "bannerURI": "https://bafybeiaaf7bfbppep5ahb2m75xlmxyj76hxo3oh5dcl4prxeskruwpe6c4.ipfs.nftstorage.link/", "tags": [], "extensions": {"discord": "https://discord.gg/aera", "twitter": "https://twitter.com/aera_football", "instagram": "https://www.instagram.com/aera_football/", "website": "http://aera.onefootbal.com"}}, {"chainId": 747, "address": "0xd796ff17107bbff6", "contractName": "Art", "path": {"storage": "/storage/versusArtCollection", "public": "/public/versusArtCollection"}, "evmAddress": "0xa3405bd059979e10f73a15b5efa21affcead8496", "flowAddress": "0xd796ff17107bbff6", "name": "Versus", "description": "Curated auction house for fine art", "logoURI": "https://pbs.twimg.com/profile_images/1295757455679528963/ibkAIRww_400x400.jpg", "bannerURI": "https://pbs.twimg.com/profile_images/1295757455679528963/ibkAIRww_400x400.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/FlowVersus", "website": "https://versus.auction"}}, {"chainId": 747, "address": "0x7620acf6d7f2468a", "contractName": "Bl0x", "path": {"storage": "/storage/bl0xNFTs", "public": "/public/bl0xNFTs"}, "evmAddress": "0xeaed52be00f58219399bf3e32a870b509d6a80d2", "flowAddress": "0x7620acf6d7f2468a", "name": "bl0x", "description": "Minting a Bl0x triggers the catalyst moment of a big bang scenario. Generating a treasure that is designed to relate specifically to its holder.", "logoURI": "https://bl0x.xyz/assets/home/<USER>", "bannerURI": "https://pbs.twimg.com/profile_banners/1535883931777892352/1661105339/1500x500", "tags": [], "extensions": {"twitter": "https://twitter.com/Bl0xNFT", "discord": "https://t.co/iY7AhEumR9", "website": "https://find.xyz/mp/bl0x"}}, {"chainId": 747, "address": "0xd45e2bd9a3d5003b", "contractName": "Bobblz_NFT", "path": {"storage": "/storage/Bobblz_NFTCollection", "public": "/public/Bobblz_NFTCollection"}, "evmAddress": "0xc91018ba5188e3863439a67c03cc894ca6de479c", "flowAddress": "0xd45e2bd9a3d5003b", "name": "Bobblz shop", "description": "This is a Bobblz shop", "logoURI": "https://media-local.gigantik.io/bobblz/square.png", "bannerURI": "https://media-local.gigantik.io/bobblz/banner.png", "tags": [], "extensions": {"twitter": "https://x.com/gigantik_", "discord": "https://discord.gg/giglabs", "instagram": "", "website": "https://Bobblz.shops.gigantik.io/"}}, {"chainId": 747, "address": "0x329feb3ab062d289", "contractName": "BreakingT_NFT", "path": {"storage": "/storage/BreakingT_NFTCollection", "public": "/public/BreakingT_NFTCollection"}, "evmAddress": "0x478a94c2b821f4864700e39f22fa6b7584df42a1", "flowAddress": "0x329feb3ab062d289", "name": "BreakingT shop", "description": "This is a BreakingT shop", "logoURI": "https://media-local.gigantik.io/breakingt/square.png", "bannerURI": "https://media-local.gigantik.io/breakingt/banner.png", "tags": [], "extensions": {"instagram": "", "twitter": "https://x.com/gigantik_", "discord": "https://discord.gg/giglabs", "website": "https://BreakingT.shops.gigantik.io/"}}, {"chainId": 747, "address": "0xf02b15e11eb3715b", "contractName": "BWAYX_NFT", "path": {"storage": "/storage/BWAYX_NFTCollection", "public": "/public/BWAYX_NFTCollection"}, "evmAddress": "0xc2ecf1de75291a37ff879234fc3053018868ffd5", "flowAddress": "0xf02b15e11eb3715b", "name": "BWAYX shop", "description": "This is a BWAYX shop", "logoURI": "https://media-local.gigantik.io/bwayx/square.png", "bannerURI": "https://media-local.gigantik.io/bwayx/banner.png", "tags": [], "extensions": {"discord": "https://discord.gg/giglabs", "twitter": "https://x.com/gigantik_", "instagram": "", "website": "https://BWAYX.shops.gigantik.io/"}}, {"chainId": 747, "address": "0x329feb3ab062d289", "contractName": "Canes_Vault_NFT", "path": {"storage": "/storage/Canes_Vault_NFTCollection", "public": "/public/Can<PERSON>_Vault_NFTCollection"}, "flowAddress": "0x329feb3ab062d289", "name": "Canes_Vault shop", "description": "This is a Canes_Vault shop", "logoURI": "https://media-local.gigantik.io/canes_vault/square.png", "bannerURI": "https://media-local.gigantik.io/canes_vault/banner.png", "tags": [], "extensions": {"twitter": "https://x.com/gigantik_", "discord": "https://discord.gg/giglabs", "instagram": "", "website": "https://Canes_Vault.shops.gigantik.io/"}}, {"chainId": 747, "address": "0xf887ece39166906e", "contractName": "Car", "path": {"storage": "/storage/CarCollection", "public": "/public/CarCollection"}, "evmAddress": "0x2d03fccc951c98ce1824299437158f9bbdbbb1ed", "flowAddress": "0xf887ece39166906e", "name": "Driverz Utility Cars", "description": "Driverz Car Collection", "logoURI": "https://driverz.world/DriverzNFT-logo.png", "bannerURI": "https://driverz.world/DriverzNFT-logo.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/driverzworld", "twitter": "https://twitter.com/DriverzWorld", "discord": "https://discord.gg/driverz", "website": "https://driverz.world/"}}, {"chainId": 747, "address": "0xf887ece39166906e", "contractName": "CarClub", "path": {"storage": "/storage/CarClubCollection", "public": "/public/CarClubCollection"}, "evmAddress": "0x9b989cd4ee12bfd0d83f74bbfbc102d042c79dd0", "flowAddress": "0xf887ece39166906e", "name": "Driverz Car Club", "description": "Driverz Car Club Collection", "logoURI": "https://driverz.world/DriverzNFT-logo.png", "bannerURI": "https://driverz.world/DriverzNFT-logo.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/driverzworld", "twitter": "https://twitter.com/DriverzWorld", "discord": "https://discord.gg/driverz", "website": "https://driverz.world/"}}, {"chainId": 747, "address": "0x097bafa4e0b48eef", "contractName": "CharityNFT", "path": {"storage": "/storage/findCharityCollection", "public": "/public/findCharityCollection"}, "evmAddress": "0x67f3e53dec8d8d16bd737dd25df43225b230a620", "flowAddress": "0x097bafa4e0b48eef", "name": "Neo Charity 2021", "description": "This collection is to show participation in the Neo Collectibles x Flowverse Charity Auction in 2021.", "logoURI": "https://pbs.twimg.com/profile_images/1467546091780550658/R1uc6dcq_400x400.jpg", "bannerURI": "https://pbs.twimg.com/profile_banners/1448245049666510848/1652452073/1500x500", "tags": [], "extensions": {"Twitter": "https://twitter.com/findonflow", "Discord": "https://discord.gg/95P274mayM", "website": "http://find.xyz/neoCharity"}}, {"chainId": 747, "address": "0xd3df824bf81910a4", "contractName": "CryptoPiggoV2", "path": {"storage": "/storage/CryptoPiggoV2Collection", "public": "/public/CryptoPiggoV2Collection"}, "evmAddress": "******************************************", "flowAddress": "0xd3df824bf81910a4", "name": "The Crypto Piggo V2 NFT Collection", "description": "", "logoURI": "https://ipfs.tenzingai.com/ipfs/QmbkTGNXowKhrxQXu4A8FwEb1Nf5YbZTtfwPoqogKrqaH4", "bannerURI": "https://ipfs.tenzingai.com/ipfs/QmbkTGNXowKhrxQXu4A8FwEb1Nf5YbZTtfwPoqogKrqaH4", "tags": [], "extensions": {"website": "https://www.rareworx.com/"}}, {"chainId": 747, "address": "0x329feb3ab062d289", "contractName": "DGD_NFT", "path": {"storage": "/storage/DGD_NFTCollection", "public": "/public/DGD_NFTCollection"}, "flowAddress": "0x329feb3ab062d289", "name": "DGD shop", "description": "This is a DGD shop", "logoURI": "https://media-local.gigantik.io/dgd/square.png", "bannerURI": "https://media-local.gigantik.io/dgd/banner.png", "tags": [], "extensions": {"instagram": "", "twitter": "https://x.com/gigantik_", "discord": "https://discord.gg/giglabs", "website": "https://DGD.shops.gigantik.io/"}}, {"chainId": 747, "address": "0xe81193c424cfd3fb", "contractName": "Doodles", "path": {"storage": "/storage/doodles", "public": "/public/doodles"}, "evmAddress": "0x422df8e241cabd526cdc08ee83bbf903ee83966c", "flowAddress": "0xe81193c424cfd3fb", "name": "Doodles", "description": "This Doodle is a uniquely personalized customizable character in a one-of-a-kind style.", "logoURI": "https://ipfs.io/ipfs/QmVpAiutpnzp3zR4q2cUedMxsZd8h5HDeyxs9x3HibsnJb", "bannerURI": "https://res.cloudinary.com/hxn7xk7oa/image/upload/v1675121458/doodles2_banner_ee7a035d05.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/Doodles", "discord": "https://discord.gg/doodles", "website": "https://doodles.app"}}, {"chainId": 747, "address": "0xa039bd7d55a96c0c", "contractName": "DriverzNFT", "path": {"storage": "/storage/DriverzNFTCollection", "public": "/public/DriverzNFTCollection"}, "evmAddress": "0xdfaa0b31907181bd370599d817563f80a3585224", "flowAddress": "0xa039bd7d55a96c0c", "name": "<PERSON><PERSON>", "description": "An exclusive collection of energetic Driverz ready to vroom vroom on FLOW.", "logoURI": "https://ipfs.io/ipfs/QmV4FsnFiU7QY8ybwd5uuXwogVo9wcRExQLwedh7HU1mrU", "bannerURI": "https://ipfs.io/ipfs/QmYn6vg1pCuKb6jWT3SDHuyX4NDyJB4wvcYarmsyppoGDS", "tags": [], "extensions": {"twitter": "https://twitter.com/DriverzWorld/", "discord": "https://discord.gg/driverz", "instagram": "https://www.instagram.com/driverzworld", "website": "https://driverz.world"}}, {"chainId": 747, "address": "0x1e4aa0b87d10b141", "contractName": "EVMVMBridgedNFT_d112634f06902a977db1d596c77715d72f8da8a9", "path": {"storage": "/storage/EVMVMBridgedNFT_d112634f06902a977db1d596c77715d72f8da8a9Collection", "public": "/public/EVMVMBridgedNFT_d112634f06902a977db1d596c77715d72f8da8a9Collection"}, "evmAddress": "0xd112634f06902a977db1d596c77715d72f8da8a9", "flowAddress": "0x1e4aa0b87d10b141", "name": "<PERSON>zie Collectibles", "description": "This NFT Collection was bridged from EVM on Flow with the ERC721 contract address of d112634f06902a977db1d596c77715d72f8da8a9", "logoURI": "https://crimson-calm-beetle-499.mypinata.cloud/ipfs/bafybeig5h2w74qmvkzffmuqnqh7d3itx5sgc3k6rvbrecaz4urif75gxmq/Beezie%20x%20Logo.png", "bannerURI": "https://crimson-calm-beetle-499.mypinata.cloud/ipfs/bafybeig5h2w74qmvkzffmuqnqh7d3itx5sgc3k6rvbrecaz4urif75gxmq/b2%20%281%29.jpg", "tags": [], "extensions": {"website": "https://port.flow.com", "displaySource": "0xa2de93114bae3e73"}}, {"chainId": 747, "address": "0x2d4c3caffbeab845", "contractName": "FLOAT", "path": {"storage": "/storage/FLOATCollectionStoragePath", "public": "/public/FLOATCollectionPublicPath"}, "evmAddress": "0x2b7cfe0f24c18690a4e34a154e313859b7c6e342", "flowAddress": "0x2d4c3caffbeab845", "name": "FLOAT", "description": "FLOAT is a proof of attendance platform on the Flow blockchain.", "logoURI": "https://i.imgur.com/v0Njnnk.png", "bannerURI": "https://i.imgur.com/ETeVZZU.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/emerald_dao", "discord": "https://discord.gg/emeraldcity", "website": "https://floats.city"}}, {"chainId": 747, "address": "0x921ea449dffec68a", "contractName": "F<PERSON>bot", "path": {"storage": "/storage/FlobotCollection", "public": "/public/FlobotCollection"}, "evmAddress": "0x37157785224b13140e6254178544a4e824867b01", "flowAddress": "0x921ea449dffec68a", "name": "Flovatar Flobot Collection", "description": "Flovatar is pioneering a new way to unleash community creativity in Web3 by allowing users to be co-creators of their prized NFTs, instead of just being passive collectors.", "logoURI": "https://images.flovatar.com/logo.svg", "bannerURI": "https://images.flovatar.com/logo-horizontal.svg", "tags": [], "extensions": {"twitter": "https://x.com/flovatar", "instagram": "https://instagram.com/flovatar_nft", "tiktok": "https://www.tiktok.com/@flovatar", "discord": "https://discord.gg/flovatar", "website": "https://flovatar.com"}}, {"chainId": 747, "address": "0x921ea449dffec68a", "contractName": "Flovatar", "path": {"storage": "/storage/FlovatarCollection", "public": "/public/FlovatarCollection"}, "evmAddress": "0x99cbb5b90b1e08164281b24ac9217eda4dc0ee62", "flowAddress": "0x921ea449dffec68a", "name": "Flovatar Collection", "description": "Flovatar is pioneering a new way to unleash community creativity in Web3 by allowing users to be co-creators of their prized NFTs, instead of just being passive collectors.", "logoURI": "https://images.flovatar.com/logo.svg", "bannerURI": "https://images.flovatar.com/logo-horizontal.svg", "tags": [], "extensions": {"twitter": "https://x.com/flovatar", "tiktok": "https://www.tiktok.com/@flovatar", "discord": "https://discord.gg/flovatar", "instagram": "https://instagram.com/flovatar_nft", "website": "https://flovatar.com"}}, {"chainId": 747, "address": "0x921ea449dffec68a", "contractName": "FlovatarComponent", "path": {"storage": "/storage/FlovatarComponentCollection", "public": "/public/FlovatarComponentCollection"}, "evmAddress": "0x478b816cfcb33bf16396c8dae59ae1dd20b15786", "flowAddress": "0x921ea449dffec68a", "name": "Flovatar Component Collection", "description": "Flovatar is pioneering a new way to unleash community creativity in Web3 by allowing users to be co-creators of their prized NFTs, instead of just being passive collectors.", "logoURI": "https://images.flovatar.com/logo.svg", "bannerURI": "https://images.flovatar.com/logo-horizontal.svg", "tags": [], "extensions": {"discord": "https://discord.gg/flovatar", "twitter": "https://x.com/flovatar", "instagram": "https://instagram.com/flovatar_nft", "tiktok": "https://www.tiktok.com/@flovatar", "website": "https://flovatar.com"}}, {"chainId": 747, "address": "0xa45ead1cf1ca9eda", "contractName": "FlowRewards", "path": {"storage": "/storage/flowRewardsCollection", "public": "/public/flowRewardsCollectionPublic"}, "evmAddress": "0x45fd22727acd6e7dce5c8f5d5b929d0f82f567ee", "flowAddress": "0xa45ead1cf1ca9eda", "name": "Flow Rewards NFT", "description": "Lock & claim FLOW rewards and more", "logoURI": "https://ipfs.io/ipfs/QmYuLdXMbmDoUjGwKxfv3Jk3cgKV6mxYBAewNpxijkYYZn", "bannerURI": "https://ipfs.io/ipfs/QmTBnCqNwgE8AiHPs9YxuVb8HSKWA2RvY5Q4NYm1tvW6wq", "tags": [], "extensions": {"github": "https://github.com/onflow/crescendo-rewards-sc", "instagram": "https://www.instagram.com/flowblockchain", "twitter": "https://twitter.com/flow_blockchain", "discord": "https://discord.com/invite/flowblockchain", "website": "https://rewards.flow.com"}}, {"chainId": 747, "address": "0x9212a87501a8a6a2", "contractName": "FlowversePass", "path": {"storage": "/storage/FlowversePassCollection", "public": "/public/FlowversePassCollection"}, "flowAddress": "0x9212a87501a8a6a2", "name": "Flowverse Mystery Pass", "description": "The Flowverse Mystery Pass is a utility-focused membership pass that aims to level up Flowverse from a web2 information provider to a web3 brand", "logoURI": "https://flowverse-mystery-pass.s3.filebase.com/mainnet/squareImage.jpg", "bannerURI": "https://flowverse-mystery-pass.s3.filebase.com/mainnet/bannerImage.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/flowverse_", "discord": "https://discord.gg/flowverse", "instagram": "https://www.instagram.com/flowverseofficial", "website": "https://nft.flowverse.co/flowverse-mystery-pass"}}, {"chainId": 747, "address": "0xce4c02539d1fabe8", "contractName": "FlowverseSocks", "path": {"storage": "/storage/MatrixMarketFlowverseSocksCollection", "public": "/public/MatrixMarketFlowverseSocksCollection"}, "evmAddress": "0xf5e1953971497b86ddc2b7a5bc61182da3cb0e47", "flowAddress": "0xce4c02539d1fabe8", "name": "Flowverse Socks", "description": "Socks by Flowverse are versatile NFTs that enable you to claim 1 limited edition pair of physical Flowverse Socks, shipped globally. The total number of Socks by Flowverse is 111.", "logoURI": "https://media.nft.matrixmarket.xyz/media/dbbrUIkarYLXKKjiY5AYk_IMAGE.png", "bannerURI": "https://media.nft.matrixmarket.xyz/media/cvR_VOmK5g6oGMhXRj1pR_IMAGE.png", "tags": [], "extensions": {"twitter": "https://twitter.com/flowverse_", "website": "https://www.socknft.com/"}}, {"chainId": 747, "address": "0x9212a87501a8a6a2", "contractName": "FlowverseTreasures", "path": {"storage": "/storage/FlowverseTreasuresCollection", "public": "/public/FlowverseTreasuresCollection"}, "flowAddress": "0x9212a87501a8a6a2", "name": "Treasures by Flowverse", "description": "Treasures is Flowverse's iconic art collector experience. These are ongoing, free digital artworks exclusively available for Flowverse Mystery Pass and Sock holders", "logoURI": "https://flowverse-treasures.s3.filebase.com/mainnet/collectionSquareImage.png", "bannerURI": "https://flowverse-treasures.s3.filebase.com/mainnet/collectionBannerImage.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/flowverseofficial", "discord": "https://discord.gg/flowverse", "twitter": "https://twitter.com/flowverse_", "website": "https://nft.flowverse.co/treasures"}}, {"chainId": 747, "address": "0xd2abb5dbf5e08666", "contractName": "FRC20SemiNFT", "path": {"storage": "/storage/FRC20SemiNFT_0xd2abb5dbf5e08666collection", "public": "/public/FRC20SemiNFT_0xd2abb5dbf5e08666collection"}, "evmAddress": "0xd5fa1aff2e92acf3f9875b54b171ee5c20fe9150", "flowAddress": "0xd2abb5dbf5e08666", "name": "FIXeS 𝔉rc20 Semi-NFT", "description": "This collection is used to wrap 𝔉rc20 token as semi-NFTs.", "logoURI": "https://i.imgur.com/hs3U5CY.png", "bannerURI": "https://i.imgur.com/4DOuqFf.jpeg", "tags": [], "extensions": {"x": "https://twitter.com/fixesWorld", "github": "https://github.com/fixes-world", "linktree": "https://linktr.ee/fixes.world", "twitter": "https://twitter.com/fixesWorld", "website": "https://fixes.world/"}}, {"chainId": 747, "address": "0xf3ee684cd0259fed", "contractName": "Fuchibola_NFT", "path": {"storage": "/storage/Fuchibola_NFTCollection", "public": "/public/Fuchibola_NFTCollection"}, "evmAddress": "0x6f7a202a1280264c0f3505c142c04e45e91c0bd3", "flowAddress": "0xf3ee684cd0259fed", "name": "Fuchibola shop", "description": "This is a Fuchibola shop", "logoURI": "https://media-local.gigantik.io/fuchibola/square.png", "bannerURI": "https://media-local.gigantik.io/fuchibola/banner.png", "tags": [], "extensions": {"instagram": "", "twitter": "https://x.com/gigantik_", "discord": "https://discord.gg/giglabs", "website": "https://Fuchibola.shops.gigantik.io/"}}, {"chainId": 747, "address": "0x34f2bf4a80bb0f69", "contractName": "GooberXContract", "path": {"storage": "/storage/GooberzPartyFolksCollection", "public": "/public/GooberzPartyFolksCollectionPublic"}, "evmAddress": "0x653e0bdcd20f84ad401d2ffa38b007486a850d5f", "flowAddress": "0x34f2bf4a80bb0f69", "name": "Party Mansion Gooberz", "description": "The Party Gooberz is a fun and comical art collection of 3550 collectibles living on the Flow Blockchain. As one of the first PFP collectibles on Flow, we enjoy bringing the party and hanging with friends. So grab a drink, pump up the music, and get ready to party because The Party Goobz are ready to go within Party Mansion!", "logoURI": "https://ipfs.io/ipfs/QmeiwpEXCidsPae3ZPpSJTKVit1R2LHiF4cw5pvmMPRC4x", "bannerURI": "https://ipfs.io/ipfs/QmdU1j5nqeQBmVWZZDhz23z6mMPwMp5i2Ka2sBpMhYggPT", "tags": [], "extensions": {"twitter": "https://mobile.twitter.com/the_goobz_nft", "discord": "http://discord.gg/zJRNqKuDQH", "website": "https://partymansion.io/"}}, {"chainId": 747, "address": "0xf887ece39166906e", "contractName": "<PERSON><PERSON><PERSON>", "path": {"storage": "/storage/HelmetCollection", "public": "/public/HelmetCollection"}, "evmAddress": "0x2c20fc197ac025ee283464ce5e49c9510c1e27aa", "flowAddress": "0xf887ece39166906e", "name": "Driverz Utility Helmet", "description": "<PERSON>z Helmet Collection", "logoURI": "https://driverz.world/DriverzNFT-logo.png", "bannerURI": "https://driverz.world/DriverzNFT-logo.png", "tags": [], "extensions": {"discord": "https://discord.gg/driverz", "twitter": "https://twitter.com/DriverzWorld", "instagram": "https://www.instagram.com/driverzworld", "website": "https://driverz.world/"}}, {"chainId": 747, "address": "0xa6ee47da88e6cbde", "contractName": "IconoGraphika", "path": {"storage": "/storage/IconoGraphikaNFT", "public": "/public/IconoGraphikaNFT"}, "evmAddress": "0xf2222eee871acf5e67128b8dc998f95ec97d3939", "flowAddress": "0xa6ee47da88e6cbde", "name": "PANTHEON COLLECTION", "description": "The Pantheon Collection is a showcase of iconic designs of the 20th century.", "logoURI": "https://ipfs.io/ipfs/QmVUqQW4KUgom5dHss5PrZ2JTyMoREuU77ecvFWodvg5zv", "bannerURI": "https://ipfs.io/ipfs/QmUnFpbkGrpvPk5DFgh2dVsmWvEg9L5kv1TcbvXyzQKaUh", "tags": [], "extensions": {"website": "https://iconographika.com/"}}, {"chainId": 747, "address": "0x699bf284101a76f1", "contractName": "JollyJokers", "path": {"storage": "/storage/JollyJokersCollection", "public": "/public/JollyJokersCollection"}, "evmAddress": "0xf71f5868299b48d542c40951d4d1e91f132a8002", "flowAddress": "0x699bf284101a76f1", "name": "Jolly <PERSON>s", "description": "The Jolly Joker Sports Society is a collection of 5,000 Jolly Jokers living on the Flow blockchain. Owning a Jolly Joker gets you access to the Own the Moment ecosystem, including analytics tools for NBA Top Shot and NFL ALL DAY, token-gated fantasy sports and poker competitions, and so much more. If you are a fan of sports, leaderboards, and fun – then the Jolly Jokers is the perfect community for you!", "logoURI": "https://otmnft-jj.s3.amazonaws.com/Jolly_Jokers.png", "bannerURI": "https://otmnft-jj.s3.amazonaws.com/Joker-Banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/jollyjokersnft", "website": "https://otmnft.com/"}}, {"chainId": 747, "address": "0x5257f1455ed366fe", "contractName": "<PERSON><PERSON><PERSON><PERSON>", "path": {"storage": "/storage/MagnetiqTokensCollection", "public": "/public/MagnetiqTokensCollection"}, "evmAddress": "0x2a4ba366e2168d02a86d072aef7b55a39c1e7da6", "flowAddress": "0x5257f1455ed366fe", "name": "MAGNETIQ", "description": "MAGNETIQ is making managing brand community engagement easy and efficient with a plug and play, blockchain powered platform.  MAGNETIQ NFTs represent your membership in brand communities.", "logoURI": "https://magnetiq-static.s3.amazonaws.com/media/public/MAGNETIQ_Square_Logo.png", "bannerURI": "https://magnetiq-static.s3.amazonaws.com/media/public/MAGNETIQ_banner.png", "tags": [], "extensions": {"discord": "https://discord.com/invite/Magnetiq", "twitter": "https://twitter.com/magnetiq_xyz", "instagram": "https://www.instagram.com/magnetiq_xyz", "website": "https://www.magnetiq.xyz/"}}, {"chainId": 747, "address": "0xd756450f386fb4ac", "contractName": "MetaverseMarket", "path": {"storage": "/storage/NftMetaverseMarketCollectionVersionTwo", "public": "/public/NftMetaverseMarketCollectionVersionTwo"}, "evmAddress": "0x63c936f8b6565b81b0b8d999b1a6d8fbbcde5444", "flowAddress": "0xd756450f386fb4ac", "name": "Ozone Metaverse Marketplace", "description": "The first ever virtual world building creator NFT marketplace on Flow. Made by creators, for creators. Instantly create listings of all media file types including 3D models which can be immediately used in virtual world building studio. Build the new metaverse economy today by becoming a creator or simply start to build worlds today. Built on Flow. Powered by Ozone.", "logoURI": "https://d19wottuqbmkwr.cloudfront.net/nft/banners1.jpg", "bannerURI": "https://d19wottuqbmkwr.cloudfront.net/nft/banners2.jpg", "tags": [], "extensions": {"discord": "https://discord.gg/ozonemetaverse", "twitter": "https://twitter.com/ozonemetaverse", "website": "https://ozonemetaverse.io"}}, {"chainId": 747, "address": "0x8ebcbfd516b1da27", "contractName": "MFLClub", "path": {"storage": "/storage/MFLClubCollection", "public": "/public/MFLClubCollection"}, "evmAddress": "0x1de931a87d389de298b150ff01d6dc3bd0b7d281", "flowAddress": "0x8ebcbfd516b1da27", "name": "MFL Club Collection", "description": "Build your own football club, make strategic decisions, and live the thrill of real competition. Join a universe where the stakes–and your rivals–are real.", "logoURI": "https://app.playmfl.com/img/mflAvatar.png", "bannerURI": "https://app.playmfl.com/img/thumbnail.png", "tags": [], "extensions": {"discord": "https://discord.gg/pEDTR4wSPr", "twitter": "https://twitter.com/playMFL", "medium": "https://medium.com/playmfl", "linkedin": "https://www.linkedin.com/company/playmfl", "website": "https://playmfl.com"}}, {"chainId": 747, "address": "0x8ebcbfd516b1da27", "contractName": "MFLPlayer", "path": {"storage": "/storage/MFLPlayerCollection", "public": "/public/MFLPlayerCollection"}, "evmAddress": "0x117cc97619c11f17333bd8229c50f0ccc600bf8c", "flowAddress": "0x8ebcbfd516b1da27", "name": "MFL Player Collection", "description": "Build your own football club, make strategic decisions, and live the thrill of real competition. Join a universe where the stakes–and your rivals–are real.", "logoURI": "https://app.playmfl.com/img/mflAvatar.png", "bannerURI": "https://app.playmfl.com/img/thumbnail.png", "tags": [], "extensions": {"linkedin": "https://www.linkedin.com/company/playmfl", "discord": "https://discord.gg/pEDTR4wSPr", "twitter": "https://twitter.com/playMFL", "medium": "https://medium.com/playmfl", "website": "https://playmfl.com"}}, {"chainId": 747, "address": "0x0b80e42aaab305f0", "contractName": "MIKOSEANFT", "path": {"storage": "/storage/MikoSeaCollection", "public": "/public/MikoSeaCollection"}, "evmAddress": "0x16d1a3e82d908651e96ba1d4338b10eb35860132", "flowAddress": "0x0b80e42aaab305f0", "name": "MikoSea", "description": "あらゆる事業者の思いを載せて神輿を担ぐ。NFT 型クラウドファンディングマーケット「MikoSea」", "logoURI": "https://mikosea.io/mikosea_1.png", "bannerURI": "https://storage.googleapis.com/studio-design-asset-files/projects/1pqD36e6Oj/s-300x50_aa59a692-741b-408b-aea3-bcd25d29c6bd.svg", "tags": [], "extensions": {"twitter": "https://twitter.com/MikoSea_io", "website": "https://mikosea.io/"}}, {"chainId": 747, "address": "0x123cb666996b8432", "contractName": "NFGv3", "path": {"storage": "/storage/nfgNFTCollection", "public": "/public/nfgNFTCollection"}, "evmAddress": "0x76da69879644c03e1cf338a9bcb57f562318c77b", "flowAddress": "0x123cb666996b8432", "name": "NonFunGerbils", "description": "The NonFunGerbils are a collaboration between the NonFunGerbils Podcast, their audience and sometimes fabolous artists. Harnessing the power of MEMEs with creative writing and collaboration they create the most dankest, cutest gerbils in the NFT space.", "logoURI": "https://ipfs.io/ipfs/QmeG1rPaLWmn4uUSjQ2Wbs7QnjxdQDyeadCGWyGwvHTB7c", "bannerURI": "https://ipfs.io/ipfs/QmWmDRnSrv8HK5QsiHwUNR4akK95WC8veydq6dnnFbMja1", "tags": [], "extensions": {"twitter": "https://twitter.com/NonFunGerbils", "website": "https://nonfungerbils.com"}}, {"chainId": 747, "address": "0x4f7ff543c936072b", "contractName": "OneShots", "path": {"storage": "/storage/TiblesOneShotsCollection", "public": "/public/TiblesOneShotsCollection"}, "evmAddress": "0xb266949a9d976d0e52cc746c2676c103757c8e11", "flowAddress": "0x4f7ff543c936072b", "name": "OneShots Comic Book Trading Cards by Tibles", "description": "OneShots is a digital trading card collecting experience by Tibles, made just for comic book fans, backed by the FLOW blockchain.", "logoURI": "https://i.tibles.com/m/oneshots-flow-icon.png", "bannerURI": "https://i.tibles.com/m/oneshots-flow-collection-banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/oneshotstibles", "website": "https://app.oneshots.com"}}, {"chainId": 747, "address": "0xe4cf4bdc1751c65d", "contractName": "PackNFT", "path": {"storage": "/storage/PackNFTCollection", "public": "/public/PackNFTCollectionPub"}, "evmAddress": "0xae799bc4db1a77960a1dd236fb6d9931b301feb1", "flowAddress": "0xe4cf4bdc1751c65d", "name": "NFL All Day Packs", "description": "Officially Licensed Digital Collectibles Featuring the NFL’s Best Highlights. Buy, Sell and Collect Your Favorite NFL Moments", "logoURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_SQUARE.png", "bannerURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_BANNER.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/nflallday/", "twitter": "https://twitter.com/NFLAllDay", "discord": "https://discord.com/invite/5K6qyTzj2k", "website": "https://nflallday.com/"}}, {"chainId": 747, "address": "0x34f2bf4a80bb0f69", "contractName": "PartyMansionDrinksContract", "path": {"storage": "/storage/PartyMansionDrinkCollection", "public": "/public/PartyMansionDrinkCollectionPublic"}, "evmAddress": "0x3e3aae8772379226665d04b736be2f7881d30ca4", "flowAddress": "0x34f2bf4a80bb0f69", "name": "Party Mansion Drinks", "description": "What is a Party without drinks!? The Party Beers are an fun art collection of whacky drinks that can only be found at the bar in Party Mansion. These collectibles were first airdropped to Party Gooberz and will be a staple in the Mansion, Drink up!", "logoURI": "https://ipfs.io/ipfs/QmSEJEwqdpotJ7RX42RKDy5sVgQzhNy3XiDmFsc81wgzNC", "bannerURI": "https://ipfs.io/ipfs/QmVyXgw67QVAU98765yfQAB1meZhnSnYVoL6mz6UpzSw7W", "tags": [], "extensions": {"twitter": "https://mobile.twitter.com/the_goobz_nft", "discord": "http://discord.gg/zJRNqKuDQH", "website": "https://partymansion.io/"}}, {"chainId": 747, "address": "0xfdc436fd7db22e01", "contractName": "Piece", "path": {"storage": "/storage/Piece_Collection0xfdc436fd7db22e01", "public": "/public/Piece_Collection0xfdc436fd7db22e01"}, "evmAddress": "0x2cd1d5cd8ab5335c66aa52cfa1af4eb0d5857eb6", "flowAddress": "0xfdc436fd7db22e01", "name": "Piece", "description": "Sell Pieces of any Tweet in seconds.", "logoURI": "https://www.piece.gg/static/media/logo.48da6adac82863dd4955abe125b5c8dd.svg", "bannerURI": "https://media.discordapp.net/attachments/1075564743152107530/1149417271597473913/Piece_collection_image.png?width=1422&height=1422", "tags": [], "extensions": {"twitter": "https://twitter.com/CreateAPiece", "website": "https://piece.gg/", "displaySource": "0xa2de93114bae3e73"}}, {"chainId": 747, "address": "0xa3eb9784ae7dc9c8", "contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": {"storage": "/storage/PuffPalzCollection", "public": "/public/PuffPalzCollection"}, "evmAddress": "0xeb7bc35d8bb7ef56bf4a3b620f65a317de915654", "flowAddress": "0xa3eb9784ae7dc9c8", "name": "NBA-Top-Shot", "description": "Welcome to Puff Palz! An NFT collection of 42 animals that enjoy the devil's lettuce Are you a Smoker, Grower, Pusher, or the Top of the Food Chain?!", "logoURI": "https://puffpalz.io/static/media/logo_trans.23a0132ea8d91f699ce0.webp", "bannerURI": "https://puffpalz.io/static/media/logo_trans.23a0132ea8d91f699ce0.webp", "tags": [], "extensions": {"twitter": "https://x.com/PuffPalz", "discord": "https://discord.gg/nDxrtnxN", "website": "https://puffpalz.io"}}, {"chainId": 747, "address": "0x329feb3ab062d289", "contractName": "RaceDay_NFT", "path": {"storage": "/storage/RaceDay_NFTCollection", "public": "/public/RaceDay_NFTCollection"}, "evmAddress": "0xc7aac70c7ca640060bb6146deb2e4549758496f6", "flowAddress": "0x329feb3ab062d289", "name": "RaceDay shop", "description": "This is the RaceDay NFT and rewards experience.", "logoURI": "https://media-local.gigantik.io/raceday/square.png", "bannerURI": "https://media-local.gigantik.io/raceday/banner.png", "tags": [], "extensions": {"discord": "https://discord.gg/jkmrMY5TGB", "instagram": "https://instagram.com/racedaynft/", "twitter": "https://x.com/RaceDayNFT", "website": "https://racedaynft.com/"}}, {"chainId": 747, "address": "0xe81193c424cfd3fb", "contractName": "Redeemables", "path": {"storage": "/storage/redeemables", "public": "/public/redeemables"}, "evmAddress": "0x2c9bd29bb1c00c0af63a3a25b64b5bfcd0e6f966", "flowAddress": "0xe81193c424cfd3fb", "name": "Redeemables", "description": "Doodles 2 lets anyone create a uniquely personalized and endlessly customizable character in a one-of-a-kind style. Wearables and other collectibles can easily be bought, traded, or sold. Doodles 2 will also incorporate collaborative releases with top brands in fashion, music, sports, gaming, and more. Redeemables are a part of the Doodles ecosystem that will allow you to turn in this NFT within a particular period of time to receive a physical collectible.", "logoURI": "https://ipfs.io/ipfs/QmVpAiutpnzp3zR4q2cUedMxsZd8h5HDeyxs9x3HibsnJb", "bannerURI": "https://ipfs.io/ipfs/QmVoTikzygffMaPcacyTjF8mQ71Eg3zsMF4p4fbsAtGQmQ", "tags": [], "extensions": {"twitter": "https://twitter.com/doodles", "discord": "https://discord.gg/doodles", "instagram": "https://www.instagram.com/thedoodles", "website": "https://doodles.app"}}, {"chainId": 747, "address": "0x396646f110afb2e6", "contractName": "RogueBunnies_NFT", "path": {"storage": "/storage/RogueBunnies_NFTCollection", "public": "/public/RogueBunnies_NFTCollection"}, "evmAddress": "0xec341ab104f217c5940788503176639ff2713d06", "flowAddress": "0x396646f110afb2e6", "name": "RogueBunnies shop", "description": "This is the RogueBunnies card collection.", "logoURI": "https://media-local.gigantik.io/roguebunnies/square.png", "bannerURI": "https://media-local.gigantik.io/roguebunnies/banner.png", "tags": [], "extensions": {"instagram": "https://instagram.com/TheRogueBunnies", "twitter": "https://x.com/theRoguebunnies", "discord": "https://discord.com/invite/qm2AbnNT9T", "website": "https://tradingcards.roguebunnies.com"}}, {"chainId": 747, "address": "0xca5c31c0c03e11be", "contractName": "Sportbit", "path": {"storage": "/storage/SportbitCollection", "public": "/public/SportbitCollection"}, "evmAddress": "0xa7b0ee5ada31ee79e83c04541eee14c6f9d92667", "flowAddress": "0xca5c31c0c03e11be", "name": "Sportvatar Accessories Collection", "description": "The Sportvatar Accessories allow you customize and make your beloved Sportvatar even more unique and exclusive.", "logoURI": "https://images.sportvatar.com/logo.svg", "bannerURI": "https://images.sportvatar.com/logo-horizontal.svg", "tags": [], "extensions": {"twitter": "https://x.com/sportvatar", "instagram": "https://instagram.com/sportvatar", "discord": "https://discord.gg/sportvatar", "tiktok": "https://www.tiktok.com/@sportvatar", "website": "https://sportvatar.com"}}, {"chainId": 747, "address": "0xca5c31c0c03e11be", "contractName": "Sportvatar", "path": {"storage": "/storage/SportvatarCollection", "public": "/public/SportvatarCollection"}, "evmAddress": "0xfbf3d60b68f3b5b0e5ca5a6430fe3439d3dd50a1", "flowAddress": "0xca5c31c0c03e11be", "name": "Sportvatar Collection", "description": "Sportvatar is the next generation of composable and customizable Digital Collectibles", "logoURI": "https://images.sportvatar.com/logo.svg", "bannerURI": "https://images.sportvatar.com/logo-horizontal.svg", "tags": [], "extensions": {"twitter": "https://x.com/sportvatar", "tiktok": "https://www.tiktok.com/@sportvatar", "instagram": "https://instagram.com/sportvatar", "discord": "https://discord.gg/sportvatar", "website": "https://sportvatar.com"}}, {"chainId": 747, "address": "0x0b2a3299cc857e29", "contractName": "TopShot", "path": {"storage": "/storage/MomentCollection", "public": "/public/MomentCollection"}, "evmAddress": "0x50ab3a827ad268e9d5a24d340108fad5c25dad5f", "flowAddress": "0x0b2a3299cc857e29", "name": "NBA-Top-Shot", "description": "NBA Top Shot is your chance to own, sell, and trade official digital collectibles of the NBA and WNBA's greatest plays and players", "logoURI": "https://nbatopshot.com/static/favicon/favicon.svg", "bannerURI": "https://nbatopshot.com/static/img/top-shot-logo-horizontal-white.svg", "tags": [], "extensions": {"discord": "https://discord.com/invite/nbatopshot", "instagram": "https://www.instagram.com/nbatopshot", "twitter": "https://twitter.com/nbatopshot", "website": "https://nbatopshot.com"}}, {"chainId": 747, "address": "0x329feb3ab062d289", "contractName": "UFC_NFT", "path": {"storage": "/storage/UFC_NFTCollection", "public": "/public/UFC_NFTCollection"}, "evmAddress": "0x8a1a321f7feb5dee3cb5b120e1f9937c4478575f", "flowAddress": "0x329feb3ab062d289", "name": "UFC Strike", "description": "Collect the most iconic video highlights from the biggest (and most notorious) fighters from UFC. UFC Strike allows fans to express their fandom like never before with fully licensed UFC video NFTs.", "logoURI": "https://media-local.gigantik.io/ufc/square.png", "bannerURI": "https://media-local.gigantik.io/ufc/banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/UFCStrikeNFT", "discord": "https://discord.gg/UFCStrike", "instagram": "https://instagram.com/ufcstrike", "website": "https://www.ufcstrike.com/"}}, {"chainId": 747, "address": "0xe81193c424cfd3fb", "contractName": "Wearables", "path": {"storage": "/storage/wearables", "public": "/public/wearables"}, "flowAddress": "0xe81193c424cfd3fb", "name": "Wearables", "description": "Doodles 2 lets anyone create a uniquely personalized and endlessly customizable character in a one-of-a-kind style. Wearables and other collectibles can easily be bought, traded, or sold. Doodles 2 will also incorporate collaborative releases with top brands in fashion, music, sports, gaming, and more.\n\nDoodles 2 Private Beta, which will offer first access to the Doodles character creator tools, will launch later in 2022. Doodles 2 Private Beta will only be available to Beta Pass holders.", "logoURI": "https://ipfs.io/ipfs/QmVpAiutpnzp3zR4q2cUedMxsZd8h5HDeyxs9x3HibsnJb", "bannerURI": "https://ipfs.io/ipfs/QmWEsThoSdJHNVcwexYuSucR4MEGhkJEH6NCzdTV71y6GN", "tags": [], "extensions": {"discord": "https://discord.gg/doodles", "twitter": "https://twitter.com/doodles", "website": "https://doodles.app"}}, {"chainId": 747, "address": "0xf887ece39166906e", "contractName": "Wheel", "path": {"storage": "/storage/WheelCollection", "public": "/public/WheelCollection"}, "evmAddress": "0xbf7b462be057adaaaabeaa320dc8ce29826238f4", "flowAddress": "0xf887ece39166906e", "name": "Driverz Utility Wheels", "description": "Driverz Wheel Collection", "logoURI": "https://driverz.world/DriverzNFT-logo.png", "bannerURI": "https://driverz.world/DriverzNFT-logo.png", "tags": [], "extensions": {"discord": "https://discord.gg/driverz", "twitter": "https://twitter.com/DriverzWorld", "instagram": "https://www.instagram.com/driverzworld", "website": "https://driverz.world/"}}, {"chainId": 747, "address": "0x62b3063fbe672fc8", "contractName": "ZeedzINO", "path": {"storage": "/storage/ZeedzINOCollection", "public": "/public/ZeedzINOCollection"}, "evmAddress": "0xd50a7eacabd654f1a1c5ea05f9c74a1022340408", "flowAddress": "0x62b3063fbe672fc8", "name": "Zeedz", "description": "Zeedz is the first play-for-purpose game where players reduce global carbon emissions by collecting plant-inspired creatures: Zeedles. They live as NFTs on an eco-friendly blockchain (Flow) and grow with the real-world weather.", "logoURI": "https://play.zeedz.io/logo-zeedz.svg", "bannerURI": "https://play.zeedz.io/background-zeedz.jpg", "tags": [], "extensions": {"youtube": "https://www.youtube.com/c/zeedz_official", "linkedin": "https://www.linkedin.com/company/zeedz", "medium": "https://blog.zeedz.io/", "instagram": "https://www.instagram.com/zeedz_official/", "discord": "http://discord.com/invite/zeedz", "twitter": "https://twitter.com/zeedz_official", "website": "https://play.zeedz.io"}}, {"chainId": 747, "address": "0x30cf5dcf6ea8d379", "contractName": "AeraRewards", "path": {"storage": "/storage/aeraRewardsNFT", "public": "/public/aeraRewardsNFT"}, "evmAddress": "0xd134864fac0dfea2319063a79368e09fa661f0b4", "flowAddress": "0x30cf5dcf6ea8d379", "name": "<PERSON><PERSON>", "description": "Aera by OneFootball", "logoURI": "https://ipfs.io/ipfs/bafkreiameqwyluog75u7zg3dmf56b5mbed7cdgv6uslkph6nvmdf2aipmy", "bannerURI": "https://ipfs.io/ipfs/bafybeiayhvr2sm4lco3tbsa74blynlnzhhzrjouteyqaq43giuyiln4xb4", "tags": [], "extensions": {"instagram": "https://www.instagram.com/aera_football/", "twitter": "https://twitter.com/aera_football", "discord": "https://discord.gg/aera", "website": "http://aera.onefootbal.com"}}, {"chainId": 747, "address": "0xabe5a2bf47ce5bf3", "contractName": "aiSportsMinter", "path": {"storage": "/storage/aiSportsMinterCollection", "public": "/public/aiSportsMinterCollection"}, "evmAddress": "0x1899cb99846f7c9891ecdd785474a5d9c2889da6", "flowAddress": "0xabe5a2bf47ce5bf3", "name": "The aiSports Collection", "description": "This collection is the home of the official aSports' NFTs.", "logoURI": "https://firebasestorage.googleapis.com/v0/b/fantasyball-6e433.appspot.com/o/upload_image.png?alt=media&token=947bf82d-b697-4cb2-b58f-17b237705ae5", "bannerURI": "https://firebasestorage.googleapis.com/v0/b/fantasyball-6e433.appspot.com/o/upload_image.png?alt=media&token=947bf82d-b697-4cb2-b58f-17b237705ae5", "tags": [], "extensions": {"twitter": "https://twitter.com/aisportspro", "website": "https://www.aisportspro.com/"}}, {"chainId": 747, "address": "0xe4cf4bdc1751c65d", "contractName": "AllDay", "path": {"storage": "/storage/AllDayNFTCollection", "public": "/public/AllDayNFTCollection"}, "evmAddress": "0x4f43e0357e7e3d61718e412fd38eaf692fac96c0", "flowAddress": "0xe4cf4bdc1751c65d", "name": "NFL All Day", "description": "Officially Licensed Digital Collectibles Featuring the NFL’s Best Highlights. Buy, Sell and Collect Your Favorite NFL Moments", "logoURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_SQUARE.png", "bannerURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_BANNER.png", "tags": [], "extensions": {"discord": "https://discord.com/invite/5K6qyTzj2k", "instagram": "https://www.instagram.com/nflallday/", "twitter": "https://twitter.com/NFLAllDay", "website": "https://nflallday.com/"}}, {"chainId": 747, "address": "0xee4567ab7f63abf2", "contractName": "BlovizeNFT", "path": {"storage": "/storage/blovizeNFTCollection", "public": "/public/blovizeNFTCollection"}, "flowAddress": "0xee4567ab7f63abf2", "name": "Blovize NFT Trophies", "description": "Blovize NFT Trophies is your chance to own, store and share your sports achievements as a digital collectible. Letting you to create your digital trophies room.", "logoURI": "https://blovize.azureedge.net/blovizenft-onflow/thumbnail-white.jpg", "bannerURI": "https://blovize.azureedge.net/blovizenft-onflow/banner-white.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/blovizelabs", "linkedin": "https://www.linkedin.com/company/blovize", "instagram": "https://www.instagram.com/blovizenftlabs", "website": "https://example-nft.onflow.org"}}, {"chainId": 747, "address": "0x7a9442be0b3c178a", "contractName": "Boneyard", "path": {"storage": "/storage/BoneyardCollection", "public": "/public/BoneyardCollection"}, "flowAddress": "0x7a9442be0b3c178a", "name": "BONEYARD COLLECTION", "description": "The Boneyard Collection is an NFT art project based on Cold War military aircraft.", "logoURI": "https://ipfs.io/ipfs/QmfFEnmDdtd79q7X4f4RX7GzFvp2UezBVaXTMz4ZibH1m1", "bannerURI": "https://ipfs.io/ipfs/Qmcin3QW2W1PcqNE6wn5Fnjvj12riPeKtV8oL1TxRLbcTV", "tags": [], "extensions": {"mastodon": "https://me.dm/@The_Boneyard", "twitter": "https://twitter.com/TheBoneyardNFT", "website": "https://www.boneyard.cloud"}}, {"chainId": 747, "address": "0x2c9de937c319468d", "contractName": "Cimelio_NFT", "path": {"storage": "/storage/Cimelio_NFTCollection", "public": "/public/Cimelio_NFTCollection"}, "evmAddress": "0xcb0fcde1736775aab08bbb443d1bca88ff1eecef", "flowAddress": "0x2c9de937c319468d", "name": "Cimelio shop", "description": "This is a Cimelio shop", "logoURI": "https://media-local.gigantik.io/cimelio/square.png", "bannerURI": "https://media-local.gigantik.io/cimelio/banner.png", "tags": [], "extensions": {"discord": "https://discord.gg/giglabs", "twitter": "https://x.com/gigantik_", "instagram": "", "website": "https://Cimelio.shops.gigantik.io/"}}, {"chainId": 747, "address": "0xd3df824bf81910a4", "contractName": "CryptoPiggo", "path": {"storage": "/storage/CryptoPiggoCollection", "public": "/public/CryptoPiggoCollection"}, "evmAddress": "******************************************", "flowAddress": "0xd3df824bf81910a4", "name": "The Crypto Piggo NFT Collection", "description": "", "logoURI": "https://ipfs.tenzingai.com/ipfs/QmUk3s7BoVSS56V2U4rxd1syVp8USUEygu7NmARppH183U", "bannerURI": "https://ipfs.tenzingai.com/ipfs/QmUk3s7BoVSS56V2U4rxd1syVp8USUEygu7NmARppH183U", "tags": [], "extensions": {"website": "https://www.rareworx.com/"}}, {"chainId": 747, "address": "0xd3df824bf81910a4", "contractName": "CryptoPiggoPotion", "path": {"storage": "/storage/CryptoPiggoPotionCollection", "public": "/public/CryptoPiggoPotionCollection"}, "evmAddress": "******************************************", "flowAddress": "0xd3df824bf81910a4", "name": "The Crypto Piggo Potion NFT Collection", "description": "", "logoURI": "https://ipfs.tenzingai.com/ipfs/QmbkTGNXowKhrxQXu4A8FwEb1Nf5YbZTtfwPoqogKrqaH4", "bannerURI": "https://ipfs.tenzingai.com/ipfs/QmbkTGNXowKhrxQXu4A8FwEb1Nf5YbZTtfwPoqogKrqaH4", "tags": [], "extensions": {"website": "https://www.rareworx.com/"}}, {"chainId": 747, "address": "0xe3ad6030cbaff1c2", "contractName": "DimensionX", "path": {"storage": "/storage/dmxCollection", "public": "/public/dmxCollection"}, "evmAddress": "******************************************", "flowAddress": "0xe3ad6030cbaff1c2", "name": "Dimension X", "description": "Dimension X is a Free-to-Play, Play-to-Earn strategic role playing game on the Flow blockchain set in the Dimension X comic book universe, where a pan-dimensional explosion created super powered humans, aliens and monsters with radical and terrifying superpowers!", "logoURI": "https://metadata.flow.dimensionxnft.com/heroes/collection_image.png", "bannerURI": "https://metadata.flow.dimensionxnft.com/heroes/collection_banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/DimensionX_NFT", "discord": "https://discord.gg/dimensionx", "website": "https://dimensionxnft.com"}}, {"chainId": 747, "address": "0x233eb012d34b0070", "contractName": "Domains", "path": {"storage": "/storage/fnsDomainCollection", "public": "/public/fnsDomainCollection"}, "evmAddress": "0xb94378faa50722cc651ac19dfad3f1f01db0e31f", "flowAddress": "0x233eb012d34b0070", "name": "The Flowns domain Collection", "description": "This collection is managed by Flowns and present the ownership of domain.", "logoURI": "https://www.flowns.org/assets/flowns_logo_light.svg", "bannerURI": "https://www.flowns.org/assets/flowns_logo_light.svg", "tags": [], "extensions": {"discord": "https://discord.gg/fXz4gBaYXd", "medium": "https://medium.com/@Flowns", "website": "https://flowns.org", "twitter": "https://twitter.com/flownsorg"}}, {"chainId": 747, "address": "0x4bbff461fa8f6192", "contractName": "FantastecNFT", "path": {"storage": "/storage/FantastecNFTCollection", "public": "/public/FantastecNFTCollection"}, "flowAddress": "0x4bbff461fa8f6192", "name": "Fantastec SWAP", "description": "Collect and Swap NFTs created exclusively for European Football clubs Real Madrid Mens and Womens, Arsenal Mens and Womens, Borussia Dortmund, and US College Athletes at Michigan State University, University of Michigan, University of Illinois, and Syracuse University.", "logoURI": "https://bafkreihvx3vfgnpn4ygfdcq4w7pdlamw4maasok7xuzcfoutm3lbitwprm.ipfs.nftstorage.link/", "bannerURI": "https://bafybeicadjtenkcpdts3rf43a7dgcjjfasihcaed46yxkdgvehj4m33ate.ipfs.nftstorage.link/", "tags": [], "extensions": {"twitter": "https://twitter.com/fantastecSWAP", "website": "https://fantastec-swap.io"}}, {"chainId": 747, "address": "0x921ea449dffec68a", "contractName": "FlovatarDustCollectible", "path": {"storage": "/storage/FlovatarDustCollectibleCollection", "public": "/public/FlovatarDustCollectibleCollection"}, "evmAddress": "0x7d16c291bd1af000ea5a5fed70ed1204235199ed", "flowAddress": "0x921ea449dffec68a", "name": "Flovatar Dust Collectible Collection", "description": "Flovatar is pioneering a new way to unleash community creativity in Web3 by allowing users to be co-creators of their prized NFTs, instead of just being passive collectors.", "logoURI": "https://images.flovatar.com/logo.svg", "bannerURI": "https://images.flovatar.com/logo-horizontal.svg", "tags": [], "extensions": {"instagram": "https://instagram.com/flovatar_nft", "discord": "https://discord.gg/flovatar", "twitter": "https://x.com/flovatar", "tiktok": "https://www.tiktok.com/@flovatar", "website": "https://flovatar.com"}}, {"chainId": 747, "address": "0x592eb32b47d8b85f", "contractName": "FlowtyWrapped", "path": {"storage": "/storage/FlowtyWrapped_0x592eb32b47d8b85f", "public": "/public/FlowtyWrapped_0x592eb32b47d8b85f"}, "evmAddress": "0x2c2e2ef011a8ad5a89f1a2d59358c455e3336250", "flowAddress": "0x592eb32b47d8b85f", "name": "<PERSON><PERSON> Wrapped", "description": "A celebration and statistical review of an exciting year on Flowty and across the Flow blockchain ecosystem.", "logoURI": "https://ipfs.io/ipfs/QmdCiwwJ7z2gQecDr6hn4pJj91miWYnFC178o9p6JKftmi", "bannerURI": "https://ipfs.io/ipfs/QmcLJhJh6yuLAoH6wWKMDS2zUv6myduXQc83zD5xv2V8tA", "tags": [], "extensions": {"twitter": "https://twitter.com/flowty_io", "website": "https://flowty.io/collection/0x592eb32b47d8b85fFlowtyWrapped"}}, {"chainId": 747, "address": "0x9212a87501a8a6a2", "contractName": "FlowverseShirt", "path": {"storage": "/storage/FlowverseShirtCollection", "public": "/public/FlowverseShirtCollection"}, "evmAddress": "0xeb073e026713a4c3b06c930f81f2057dfbdb513f", "flowAddress": "0x9212a87501a8a6a2", "name": "Flowverse Shirt", "description": "The Flowverse Shirt is the official shirt collection for the Flowverse community. Join a group of die-hard Flow enthusiasts and rep the Flowverse Shirt on the Flow Blockchain", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmeFH4AXFLkCzKJ64nRmtRyqxXsVH8N98QDZcUNwJphXBz", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmSevyXCcgmHse2TTc6mtK2sdAGDSWncERUXrFYP2hxMgu", "tags": [], "extensions": {"twitter": "https://twitter.com/flowverse_", "website": "https://twitter.com/flowverse_"}}, {"chainId": 747, "address": "0x8b148183c28ff88f", "contractName": "Gaia", "path": {"storage": "/storage/GaiaCollection001", "public": "/public/GaiaCollection001"}, "evmAddress": "0x798b0effe43a1144acf1be107f2e04bc5688fab6", "flowAddress": "0x8b148183c28ff88f", "name": "Ball<PERSON>z", "description": "A basketball-inspired generative NFT living on the Flow blockchain", "logoURI": "https://ballerz.com/images/onchain/logo-stack.png", "bannerURI": "https://ballerz.com/images/onchain/logo.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/@BALLERZ_NFT", "website": "https://flowty.io/collection/0x8b148183c28ff88f"}}, {"chainId": 747, "address": "0x87ca73a41bb50ad5", "contractName": "<PERSON><PERSON><PERSON><PERSON>", "path": {"storage": "/storage/GolazosNFTCollection", "public": "/public/GolazosNFTCollection"}, "evmAddress": "0xf68fafbe9d39fd7cfa69257c2ab1067281b64b87", "flowAddress": "0x87ca73a41bb50ad5", "name": "Laliga Golazos", "description": "Collect LaLiga's biggest Moments and get closer to the game than ever before", "logoURI": "https://assets.laligagolazos.com/static/golazos-logos/Golazos_Logo_Primary_B.png", "bannerURI": "https://assets.laligagolazos.com/static/golazos-logos/Golazos_Logo_Horizontal_B.png", "tags": [], "extensions": {"discord": "https://discord.gg/LaLigaGolazos", "twitter": "https://twitter.com/LaLigaGolazos", "instagram": " https://instagram.com/laligaonflow", "facebook": "https://www.facebook.com/LaLigaGolazos/", "website": "https://laligagolazos.com/"}}, {"chainId": 747, "address": "0x1dc37ab51a54d83f", "contractName": "HeroesOfTheFlow", "path": {"storage": "/storage/HeroesOfTheFlowCollection", "public": "/public/HeroesOfTheFlowCollection"}, "evmAddress": "0x43ece1a944d3a049c873bca564085d28efd111fd", "flowAddress": "0x1dc37ab51a54d83f", "name": "Heroes of the Flow", "description": "Heroes of the Flow is a post-apocalyptic auto-battler set in the Rogues universe.", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmU7a1eLvsmLda1VPe2ioikeWmhPwk5Xm7eV2iBUuirm55", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmNMek1Q2i3MoGwz7bDVAU6mCMWByqpmEhk1TFLpNXEcEF", "tags": [], "extensions": {"twitter": "https://twitter.com/heroesoftheflow", "website": "https://twitter.com/heroesoftheflow"}}, {"chainId": 747, "address": "0xd0bcefdf1e67ea85", "contractName": "HWGarageCard", "path": {"storage": "/storage/HWGarageCardCollection", "public": "/public/HWGarageCardCollection"}, "evmAddress": "0x7a7bd3afc37be2402b83d667d4fbf57d671bafab", "flowAddress": "0xd0bcefdf1e67ea85", "name": "Hot Wheels Garage Card", "description": "Digital Collectable from Hot Wheels Garage", "logoURI": "", "bannerURI": "", "tags": [], "extensions": {"facebook": "https://www.facebook.com/hotwheels", "instagram": "https://www.instagram.com/hotwheelsofficial/", "discord": "https://discord.gg/mattel", "twitter": "https://twitter.com/Hot_Wheels", "website": ""}}, {"chainId": 747, "address": "0x82ed1b9cba5bb1b3", "contractName": "KaratNFT", "path": {"storage": "/storage/KaratNFTCollection", "public": "/public/KaratNFTCollection"}, "evmAddress": "0x12782c53bfb950010549f538b8542898d907bf6e", "flowAddress": "0x82ed1b9cba5bb1b3", "name": "The 24Karat Collection", "description": "This collection is used for 24Karat market.", "logoURI": "https://static.wixstatic.com/media/568614_5676993b65c64da29cecf24d323fd664~mv2.png/v1/fill/w_140,h_160,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/568614_5676993b65c64da29cecf24d323fd664~mv2.png", "bannerURI": "https://static.wixstatic.com/media/568614_5676993b65c64da29cecf24d323fd664~mv2.png/v1/fill/w_140,h_160,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/568614_5676993b65c64da29cecf24d323fd664~mv2.png", "tags": [], "extensions": {"twitter": "https://twitter.com/24karat_io", "website": "https://market.24karat.io"}}, {"chainId": 747, "address": "0x5eb12ad3d5a99945", "contractName": "KeeprItems", "path": {"storage": "/storage/KeeprItemsCollectionV10", "public": "/public/KeeprItemsCollectionV10"}, "evmAddress": "0x24212c12535b3d0e39cf5aec81e87446d91e8dcc", "flowAddress": "0x5eb12ad3d5a99945", "name": "The Keepr Collection", "description": "This collection is used as an example to help you develop your next Flow NFT.", "logoURI": "https://firebasestorage.googleapis.com/v0/b/keepr-86355.appspot.com/o/static%2Flogo-dark.svg?alt=media&token=9d66d7ea-9b3e-4fe0-8604-04df064af359", "bannerURI": "https://firebasestorage.googleapis.com/v0/b/keepr-86355.appspot.com/o/static%2Flogo-dark.svg?alt=media&token=9d66d7ea-9b3e-4fe0-8604-04df064af359", "tags": [], "extensions": {"twitter": "https://twitter.com/keeprGG", "website": "https://keepr.gg/"}}, {"chainId": 747, "address": "0x0d77ec47bbad8ef6", "contractName": "MatrixWorldVoucher", "path": {"storage": "/storage/MatrixWorldVoucherCollection", "public": "/public/MatrixWorldVoucherCollection"}, "evmAddress": "0x279443ba076c7370cd2808bdd617596f3ca28fde", "flowAddress": "0x0d77ec47bbad8ef6", "name": "WORLD3 Land Voucher", "description": "WORLD3 Land Voucher is a collection of NFTs that represent the ownership of a piece of land in the WORLD3 Metaverse. Each NFT is unique and can be used to claim the ownership of a specific piece of land in the WORLD3 Space.", "logoURI": "https://world3.ai/assets/WORLD3Logo_black.png", "bannerURI": "https://world3.ai/assets/Matrixworld_poster.png", "tags": [], "extensions": {"twitter": "https://x.com/WORLD3_AI", "website": "https://world3.ai/"}}, {"chainId": 747, "address": "0x8ebcbfd516b1da27", "contractName": "MFLPack", "path": {"storage": "/storage/MFLPackCollection", "public": "/public/MFLPackCollection"}, "evmAddress": "0xd77da66125bb2d9ace3bd703b17a231eee9f093b", "flowAddress": "0x8ebcbfd516b1da27", "name": "MFL Pack Collection", "description": "Build your own football club, make strategic decisions, and live the thrill of real competition. Join a universe where the stakes–and your rivals–are real.", "logoURI": "https://app.playmfl.com/img/mflAvatar.png", "bannerURI": "https://app.playmfl.com/img/thumbnail.png", "tags": [], "extensions": {"discord": "https://discord.gg/pEDTR4wSPr", "twitter": "https://twitter.com/playMFL", "medium": "https://medium.com/playmfl", "linkedin": "https://www.linkedin.com/company/playmfl", "website": "https://playmfl.com"}}, {"chainId": 747, "address": "0x9212a87501a8a6a2", "contractName": "Ordinal", "path": {"storage": "/storage/OrdinalCollection", "public": "/public/OrdinalCollection"}, "evmAddress": "0x83bdd800526cc870624c07bd22c09f82f0b3e76c", "flowAddress": "0x9212a87501a8a6a2", "name": "Ordinals", "description": "Ordinals on the Flow blockchain", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmQ45TvzGVTmoMCfGqxgbiMmR4rdmSHAhz661bPyUfFrAT", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmaTj276rAUFoFiik84xCx1PYZnqZHcGp78vG6xqHLfoXo", "tags": [], "extensions": {"discord": "https://discord.gg/flowverse", "instagram": "https://www.instagram.com/flowverseofficial", "twitter": "https://twitter.com/flowverse_", "website": "https://twitter.com/flowverse_"}}, {"chainId": 747, "address": "0x321d8fcde05f6e8c", "contractName": "Seussibles", "path": {"storage": "/storage/TiblesSeussiblesCollection", "public": "/public/TiblesSeussiblesCollection"}, "evmAddress": "0x3f013cd02c8803217366ee9587fde1652ac5a57e", "flowAddress": "0x321d8fcde05f6e8c", "name": "Seussibles! Dr<PERSON> Collection by T<PERSON>s", "description": "An officially licensed <PERSON><PERSON> digital collecting experience by Tibles. Available on iOS and web, Seussibles! is super accessible, engaging, and fun!", "logoURI": "https://i.tibles.com/m/seussibles-flow-icon.png", "bannerURI": "https://i.tibles.com/m/seussibles-flow-collection-banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/seussibles", "discord": "https://discord.gg/tibles", "website": "https://seussibles.com"}}, {"chainId": 747, "address": "0x0a2fbb92a8ae5c6d", "contractName": "Sk8tibles", "path": {"storage": "/storage/TiblesSk8tiblesCollection", "public": "/public/TiblesSk8tiblesCollection"}, "evmAddress": "0x2aae88007dbdb7a06506cd4ffcf11e27cde5dcd5", "flowAddress": "0x0a2fbb92a8ae5c6d", "name": "Sk8tibles Collection", "description": "A digital trading card collecting experience by Tibles, made in partnership with the Skateboarding Hall of Fame and Museum.", "logoURI": "https://i.tibles.com/m/sk8tibles-flow-icon.png", "bannerURI": "https://i.tibles.com/m/sk8tibles-flow-collection-banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/sk8tibles", "discord": "https://discord.gg/tibles", "website": "https://sk8tibles.com"}}, {"chainId": 747, "address": "0x5b82f21c0edf76e3", "contractName": "StarlyCard", "path": {"storage": "/storage/starlyCardCollection", "public": "/public/starlyCardCollection"}, "evmAddress": "0xc4ddf229e021fc079c2f14d5624abfb99619e41c", "flowAddress": "0x5b82f21c0edf76e3", "name": "<PERSON><PERSON>", "description": "Starly is a launchpad and marketplace for gamified NFT collections on Flow.", "logoURI": "https://storage.googleapis.com/starly-prod.appspot.com/assets/starly-square-logo.jpg", "bannerURI": "https://storage.googleapis.com/starly-prod.appspot.com/assets/starly-banner.jpg", "tags": [], "extensions": {"medium": "https://medium.com/@StarlyNFT", "twitter": "https://twitter.com/StarlyNFT", "discord": "https://discord.gg/starly", "website": "https://starly.io"}}, {"chainId": 747, "address": "0x427ceada271aa0b1", "contractName": "SturdyItems", "path": {"storage": "/storage/SturdyItemsCollection", "public": "/public/SturdyItemsCollection"}, "evmAddress": "0xa7f6f20aef160b7d6ae6faf35b752e8822172cd9", "flowAddress": "0x427ceada271aa0b1", "name": "Hoodlums", "description": "Hoodlums NFT is a generative art project featuring 5,000 unique Hoodlum PFPs, crafted from hand-drawn traits by renowned memelord Somehoodlum. Created for creatives, by creatives, the project is owned and operated by Hoodlums holders through Hoodlums DAO. Hoodlums is the first PFP on the Flow Blockchain, minted in September 2021.", "logoURI": "https://ipfs.io/ipfs/QmYQPsikmJxRAtCFGTa3coUoG6bZqduyckAwodUQ35T8p9", "bannerURI": "https://ipfs.io/ipfs/QmPqVFuM2d4bSqFCjTddajaSb7AVYpDrRJuw3BeE8s1cRJ", "tags": [], "extensions": {"twitter": "https://x.com/HoodlumsNFT", "discord": "https://discord.gg/ah2jynWk", "website": "https://www.hoodlums.io/"}}], "totalAmount": 77, "filterType": "ALL", "timestamp": "2024-12-25T01:15:40.490Z", "tags": {"stablecoin": {"name": "stablecoin", "description": "Tokens that are fixed to an external asset, e.g. the US dollar"}, "ethereum": {"name": "ethereum", "description": "Asset bridged from ethereum"}, "wrapped-celer": {"name": "wrapped-celer", "description": "Asset wrapped using celer bridge"}, "utility-token": {"name": "utility-token", "description": "Tokens that are designed to be spent within a certain blockchain ecosystem"}, "governance-token": {"name": "governance-token", "description": "Tokens that are designed to be use in community governance and maintenance"}, "memecoin": {"name": "memecoin", "description": "Tokens that are created for fun and meme"}}, "version": {"major": 1, "minor": 0, "patch": 1}}