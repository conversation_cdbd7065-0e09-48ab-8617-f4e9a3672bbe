{"name": "Flow NFT List", "network": "testnet", "chainId": 545, "tokens": [{"chainId": 545, "address": "0x4dfd62c88d1b6462", "contractName": "AllDay", "path": {"storage": "/storage/AllDayNFTCollection", "public": "/public/AllDayNFTCollection"}, "flowAddress": "0x4dfd62c88d1b6462", "name": "NFL All Day", "description": "Officially Licensed Digital Collectibles Featuring the NFL’s Best Highlights. Buy, Sell and Collect Your Favorite NFL Moments", "logoURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_SQUARE.png", "bannerURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_BANNER.png", "tags": [], "extensions": {"discord": "https://discord.com/invite/5K6qyTzj2k", "instagram": "https://www.instagram.com/nflallday/", "twitter": "https://twitter.com/NFLAllDay", "website": "https://nflallday.com/"}}, {"chainId": 545, "address": "0x46664e2033f9853d", "contractName": "DimensionX", "path": {"storage": "/storage/dmxCollection", "public": "/public/dmxCollection"}, "evmAddress": "0x7c2fff21cdecd4e779f1710e101dd53556b1bdf5", "flowAddress": "0x46664e2033f9853d", "name": "Dimension X", "description": "Dimension X is a Free-to-Play, Play-to-Earn strategic role playing game on the Flow blockchain set in the Dimension X comic book universe, where a pan-dimensional explosion created super powered humans, aliens and monsters with radical and terrifying superpowers!", "logoURI": "https://metadata.dmx.shmuppyships.com/heroes/collection_image.png", "bannerURI": "https://metadata.dmx.shmuppyships.com/heroes/collection_banner.png", "tags": [], "extensions": {"discord": "https://discord.gg/dimensionx", "twitter": "https://twitter.com/DimensionX_NFT", "website": "https://dimensionxnft.com"}}, {"chainId": 545, "address": "0x46664e2033f9853d", "contractName": "DimensionXComics", "path": {"storage": "/storage/dmxComicsCollection", "public": "/public/dmxComicsCollection"}, "flowAddress": "0x46664e2033f9853d", "name": "Dimension X", "description": "Dimension X is a Free-to-Play, Play-to-Earn strategic role playing game on the Flow blockchain set in the Dimension X comic book universe, where a pan-dimensional explosion created super powered humans, aliens and monsters with radical and terrifying superpowers!", "logoURI": "https://metadata.dmx.shmuppyships.com/comics/collection_image.png", "bannerURI": "https://metadata.dmx.shmuppyships.com/comics/collection_banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/DimensionX_NFT", "discord": "https://discord.gg/dimensionx", "website": "https://dimensionxnft.com"}}, {"chainId": 545, "address": "0x1c5033ad60821c97", "contractName": "Doodles", "path": {"storage": "/storage/doodles", "public": "/public/doodles"}, "flowAddress": "0x1c5033ad60821c97", "name": "Doodles", "description": "This Doodle is a uniquely personalized customizable character in a one-of-a-kind style.", "logoURI": "https://ipfs.io/ipfs/QmVpAiutpnzp3zR4q2cUedMxsZd8h5HDeyxs9x3HibsnJb", "bannerURI": "https://res.cloudinary.com/hxn7xk7oa/image/upload/v1675121458/doodles2_banner_ee7a035d05.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/Doodles", "discord": "https://discord.gg/doodles", "website": "https://doodles.app"}}, {"chainId": 545, "address": "0x9392a4a7c3f49a0b", "contractName": "FlovatarComponent", "path": {"storage": "/storage/FlovatarComponentCollection", "public": "/public/FlovatarComponentCollection"}, "evmAddress": "0x56625ba737056dbadafb36870c4105a3a4d62bd7", "flowAddress": "0x9392a4a7c3f49a0b", "name": "Flovatar Component Collection", "description": "Flovatar is pioneering a new way to unleash community creativity in Web3 by allowing users to be co-creators of their prized NFTs, instead of just being passive collectors.", "logoURI": "https://images.flovatar.com/logo.svg", "bannerURI": "https://images.flovatar.com/logo-horizontal.svg", "tags": [], "extensions": {"tiktok": "https://www.tiktok.com/@flovatar", "instagram": "https://instagram.com/flovatar_nft", "twitter": "https://x.com/flovatar", "discord": "https://discord.gg/flovatar", "website": "https://flovatar.com"}}, {"chainId": 545, "address": "0x2e7cfb413f04382f", "contractName": "FlowRewards", "path": {"storage": "/storage/flowRewardsCollection", "public": "/public/flowRewardsCollectionPublic"}, "evmAddress": "0x12a7c2485a3669f4c83b4ab92d9ddeef032bd404", "flowAddress": "0x2e7cfb413f04382f", "name": "Flow Rewards NFT", "description": "Lock & claim FLOW rewards and more", "logoURI": "https://ipfs.io/ipfs/QmYuLdXMbmDoUjGwKxfv3Jk3cgKV6mxYBAewNpxijkYYZn", "bannerURI": "https://ipfs.io/ipfs/QmTBnCqNwgE8AiHPs9YxuVb8HSKWA2RvY5Q4NYm1tvW6wq", "tags": [], "extensions": {"github": "https://github.com/onflow/crescendo-rewards-sc", "instagram": "https://www.instagram.com/flowblockchain", "twitter": "https://twitter.com/flow_blockchain", "discord": "https://discord.com/invite/flowblockchain", "website": "https://rewards.flow.com"}}, {"chainId": 545, "address": "0xd9c02cdacccb25ab", "contractName": "FlowtyTestNFT", "path": {"storage": "/storage/FlowtyTestNFTCollection", "public": "/public/FlowtyTestNFTCollection"}, "evmAddress": "0x6bd1f8bb1c65147143a1949cfa20b6e9d3625648", "flowAddress": "0xd9c02cdacccb25ab", "name": "Flowty Test NFT Collection", "description": "This collection is used for testing things out on flowty.", "logoURI": "https://storage.googleapis.com/flowty-images/flowty-logo.jpeg", "bannerURI": "https://storage.googleapis.com/flowty-images/flowty-banner.jpeg", "tags": [], "extensions": {"twitter": "https://twitter.com/flowty_io", "website": "https://flowty.io/"}}, {"chainId": 545, "address": "0xc7c122b5b811de8e", "contractName": "FlowversePass", "path": {"storage": "/storage/FlowversePassCollection", "public": "/public/FlowversePassCollection"}, "evmAddress": "0x456cdc1e94c00fed6eac4b72765af90876695bd1", "flowAddress": "0xc7c122b5b811de8e", "name": "Flowverse Mystery Pass", "description": "The Flowverse Mystery Pass is a utility-focused membership pass that aims to level up Flowverse from a web2 information provider to a web3 brand", "logoURI": "https://flowverse-mystery-pass.s3.filebase.com/testnet/squareImage.jpg", "bannerURI": "https://flowverse-mystery-pass.s3.filebase.com/testnet/bannerImage.jpg", "tags": [], "extensions": {"twitter": "https://twitter.com/flowverse_", "instagram": "https://www.instagram.com/flowverseofficial", "discord": "https://discord.gg/flowverse", "website": "https://nft.flowverse.co/flowverse-mystery-pass"}}, {"chainId": 545, "address": "0xc7c122b5b811de8e", "contractName": "FlowverseShirt", "path": {"storage": "/storage/FlowverseShirtCollection", "public": "/public/FlowverseShirtCollection"}, "evmAddress": "0x138ff43a4b9ee2daf9fdc622c8fac1322c8df8fa", "flowAddress": "0xc7c122b5b811de8e", "name": "Flowverse Shirt", "description": "The Flowverse Shirt is the official shirt collection for the Flowverse community. Join a group of die-hard Flow enthusiasts and rep the Flowverse Shirt on the Flow Blockchain", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmeFH4AXFLkCzKJ64nRmtRyqxXsVH8N98QDZcUNwJphXBz", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmSevyXCcgmHse2TTc6mtK2sdAGDSWncERUXrFYP2hxMgu", "tags": [], "extensions": {"twitter": "https://twitter.com/flowverse_", "website": "https://twitter.com/flowverse_"}}, {"chainId": 545, "address": "0xc7c122b5b811de8e", "contractName": "FlowverseTreasures", "path": {"storage": "/storage/FlowverseTreasuresCollection", "public": "/public/FlowverseTreasuresCollection"}, "flowAddress": "0xc7c122b5b811de8e", "name": "Treasures by Flowverse", "description": "Treasures is Flowverse's iconic art collector experience. These are ongoing, free digital artworks exclusively available for Flowverse Mystery Pass and Sock holders", "logoURI": "https://flowverse-treasures.s3.filebase.com/mainnet/collectionSquareImage.png", "bannerURI": "https://flowverse-treasures.s3.filebase.com/mainnet/collectionBannerImage.png", "tags": [], "extensions": {"discord": "https://discord.gg/flowverse", "twitter": "https://twitter.com/flowverse_", "instagram": "https://www.instagram.com/flowverseofficial", "website": "https://nft.flowverse.co/treasures"}}, {"chainId": 545, "address": "0xb7248baa24a95c3f", "contractName": "FRC20SemiNFT", "path": {"storage": "/storage/FRC20SemiNFT_0xb7248baa24a95c3fcollection", "public": "/public/FRC20SemiNFT_0xb7248baa24a95c3fcollection"}, "flowAddress": "0xb7248baa24a95c3f", "name": "FIXeS 𝔉rc20 Semi-NFT", "description": "This collection is used to wrap 𝔉rc20 token as semi-NFTs.", "logoURI": "https://i.imgur.com/hs3U5CY.png", "bannerURI": "https://i.imgur.com/4DOuqFf.jpeg", "tags": [], "extensions": {"x": "https://twitter.com/fixesWorld", "github": "https://github.com/fixes-world", "linktree": "https://linktr.ee/fixes.world", "twitter": "https://twitter.com/fixesWorld", "website": "https://fixes.world/"}}, {"chainId": 545, "address": "0x1271da8a94edb0ff", "contractName": "<PERSON><PERSON><PERSON><PERSON>", "path": {"storage": "/storage/GolazosNFTCollection", "public": "/public/GolazosNFTCollection"}, "flowAddress": "0x1271da8a94edb0ff", "name": "Laliga Golazos", "description": "Collect LaLiga's biggest Moments and get closer to the game than ever before", "logoURI": "https://assets.laligagolazos.com/static/golazos-logos/Golazos_Logo_Primary_B.png", "bannerURI": "https://assets.laligagolazos.com/static/golazos-logos/Golazos_Logo_Horizontal_B.png", "tags": [], "extensions": {"facebook": "https://www.facebook.com/LaLigaGolazos/", "twitter": "https://twitter.com/LaLigaGolazos", "instagram": " https://instagram.com/laligaonflow", "discord": "https://discord.gg/LaLigaGolazos", "website": "https://laligagolazos.com/"}}, {"chainId": 545, "address": "0x2d59ec5158e3adae", "contractName": "HeroesOfTheFlow", "path": {"storage": "/storage/HeroesOfTheFlowCollection", "public": "/public/HeroesOfTheFlowCollection"}, "flowAddress": "0x2d59ec5158e3adae", "name": "Heroes of the Flow", "description": "Heroes of the Flow is a post-apocalyptic auto-battler set in the Rogues universe.", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmU7a1eLvsmLda1VPe2ioikeWmhPwk5Xm7eV2iBUuirm55", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmNMek1Q2i3MoGwz7bDVAU6mCMWByqpmEhk1TFLpNXEcEF", "tags": [], "extensions": {"twitter": "https://twitter.com/heroesoftheflow", "website": "https://twitter.com/heroesoftheflow"}}, {"chainId": 545, "address": "0x08b1f9c0bc04f36f", "contractName": "IconoGraphika", "path": {"storage": "/storage/IconoGraphikaNFT", "public": "/public/IconoGraphikaNFT"}, "flowAddress": "0x08b1f9c0bc04f36f", "name": "PANTHEON COLLECTION", "description": "The Pantheon Collection is a showcase of iconic designs of the 20th century.", "logoURI": "https://ipfs.io/ipfs/QmVUqQW4KUgom5dHss5PrZ2JTyMoREuU77ecvFWodvg5zv", "bannerURI": "https://ipfs.io/ipfs/QmUnFpbkGrpvPk5DFgh2dVsmWvEg9L5kv1TcbvXyzQKaUh", "tags": [], "extensions": {"website": "https://iconographika.com/"}}, {"chainId": 545, "address": "0xe9760069d688ef5e", "contractName": "JollyJokers", "path": {"storage": "/storage/JollyJokersCollection", "public": "/public/JollyJokersCollection"}, "flowAddress": "0xe9760069d688ef5e", "name": "Jolly <PERSON>s", "description": "The Jolly Joker Sports Society is a collection of 5,000 Jolly Jokers living on the Flow blockchain. Owning a Jolly Joker gets you access to the Own the Moment ecosystem, including analytics tools for NBA Top Shot and NFL ALL DAY, token-gated fantasy sports and poker competitions, and so much more. If you are a fan of sports, leaderboards, and fun – then the Jolly Jokers is the perfect community for you!", "logoURI": "https://otmnft-jj.s3.amazonaws.com/Jolly_Jokers.png", "bannerURI": "https://otmnft-jj.s3.amazonaws.com/Joker-Banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/jollyjokersnft", "website": "https://otmnft.com/"}}, {"chainId": 545, "address": "0x683564e46977788a", "contractName": "MFLPack", "path": {"storage": "/storage/MFLPackCollection", "public": "/public/MFLPackCollection"}, "flowAddress": "0x683564e46977788a", "name": "MFL Pack Collection", "description": "Build your own football club, make strategic decisions, and live the thrill of real competition. Join a universe where the stakes–and your rivals–are real.", "logoURI": "https://app.playmfl.com/img/mflAvatar.png", "bannerURI": "https://app.playmfl.com/img/thumbnail.png", "tags": [], "extensions": {"linkedin": "https://www.linkedin.com/company/playmfl", "medium": "https://medium.com/playmfl", "twitter": "https://twitter.com/playMFL", "discord": "https://discord.gg/pEDTR4wSPr", "website": "https://playmfl.com"}}, {"chainId": 545, "address": "0x683564e46977788a", "contractName": "MFLPlayer", "path": {"storage": "/storage/MFLPlayerCollection", "public": "/public/MFLPlayerCollection"}, "flowAddress": "0x683564e46977788a", "name": "MFL Player Collection", "description": "Build your own football club, make strategic decisions, and live the thrill of real competition. Join a universe where the stakes–and your rivals–are real.", "logoURI": "https://app.playmfl.com/img/mflAvatar.png", "bannerURI": "https://app.playmfl.com/img/thumbnail.png", "tags": [], "extensions": {"medium": "https://medium.com/playmfl", "twitter": "https://twitter.com/playMFL", "linkedin": "https://www.linkedin.com/company/playmfl", "discord": "https://discord.gg/pEDTR4wSPr", "website": "https://playmfl.com"}}, {"chainId": 545, "address": "0x04625c28593d9408", "contractName": "nba_NFT", "path": {"storage": "/storage/nba_NFTCollection", "public": "/public/nba_NFTCollection"}, "flowAddress": "0x04625c28593d9408", "name": "NBATopShot Team VIP AllStars", "description": "Collection of NBATopShot AllStar VIP Team NFTs", "logoURI": "https://media-local.gigantik.io/nba/square.png", "bannerURI": "https://media-local.gigantik.io/nba/banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/nbatopshot", "instagram": "https://www.instagram.com/nbatopshot", "discord": "https://discord.gg/nbatopshot", "website": "https://nbatopshot.com/"}}, {"chainId": 545, "address": "0xc7c122b5b811de8e", "contractName": "Ordinal", "path": {"storage": "/storage/OrdinalCollection", "public": "/public/OrdinalCollection"}, "flowAddress": "0xc7c122b5b811de8e", "name": "Ordinals", "description": "Ordinals on the Flow blockchain", "logoURI": "https://flowverse.myfilebase.com/ipfs/QmQ45TvzGVTmoMCfGqxgbiMmR4rdmSHAhz661bPyUfFrAT", "bannerURI": "https://flowverse.myfilebase.com/ipfs/QmaTj276rAUFoFiik84xCx1PYZnqZHcGp78vG6xqHLfoXo", "tags": [], "extensions": {"instagram": "https://www.instagram.com/flowverseofficial", "twitter": "https://twitter.com/flowverse_", "discord": "https://discord.gg/flowverse", "website": "https://twitter.com/flowverse_"}}, {"chainId": 545, "address": "0x4dfd62c88d1b6462", "contractName": "PackNFT", "path": {"storage": "/storage/PackNFTCollection", "public": "/public/PackNFTCollectionPub"}, "flowAddress": "0x4dfd62c88d1b6462", "name": "NFL All Day Packs", "description": "Officially Licensed Digital Collectibles Featuring the NFL’s Best Highlights. Buy, Sell and Collect Your Favorite NFL Moments", "logoURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_SQUARE.png", "bannerURI": "https://assets.nflallday.com/flow/catalogue/NFLAD_BANNER.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/nflallday/", "twitter": "https://twitter.com/NFLAllDay", "discord": "https://discord.com/invite/5K6qyTzj2k", "website": "https://nflallday.com/"}}, {"chainId": 545, "address": "0x58b60c5240d3f39b", "contractName": "TicalUniverse", "path": {"storage": "/storage/TicalUniverseCollection", "public": "/public/TicalUniverseCollection"}, "flowAddress": "0x58b60c5240d3f39b", "name": "Tical Universe", "description": "The Genesis NFT drop represents the dawn of Method Man’s Tical Universe and the birth and introduction of the original Tical Universe heroes and villains.", "logoURI": "https://tunegonft.com/assets/images/collections-page/tical.png", "bannerURI": "https://tunegonft.com/assets/images/collections-page/tical.png", "tags": [], "extensions": {"website": "https://www.tunegonft.com/collection-details/95487a67-a66a-43d5-913d-46fa8a644f4c"}}, {"chainId": 545, "address": "0x2dcd833119c0570c", "contractName": "toddnewstaging2_NFT", "path": {"storage": "/storage/toddnewstaging2_NFTCollection", "public": "/public/toddnewstaging2_NFTCollection"}, "flowAddress": "0x2dcd833119c0570c", "name": "toddnewstaging2 shop", "description": "This is a toddnewstaging2 testnet shop", "logoURI": "https://media-local.gigantik.io/toddnewstaging2/square.png", "bannerURI": "https://media-local.gigantik.io/toddnewstaging2/banner.png", "tags": [], "extensions": {"instagram": "https://instagram.com/ufcstrike", "twitter": "https://twitter.com/UFCStrikeNFT", "discord": "https://discord.gg/UFCStrike", "website": "https://toddnewstaging2.shops-staging.gigantik.io/"}}, {"chainId": 545, "address": "0x877931736ee77cff", "contractName": "TopShot", "path": {"storage": "/storage/MomentCollection", "public": "/public/MomentCollection"}, "flowAddress": "0x877931736ee77cff", "name": "NBA-Top-Shot", "description": "NBA Top Shot is your chance to own, sell, and trade official digital collectibles of the NBA and WNBA's greatest plays and players", "logoURI": "https://nbatopshot.com/static/favicon/favicon.svg", "bannerURI": "https://nbatopshot.com/static/img/top-shot-logo-horizontal-white.svg", "tags": [], "extensions": {"twitter": "https://twitter.com/nbatopshot", "instagram": "https://www.instagram.com/nbatopshot", "discord": "https://discord.com/invite/nbatopshot", "website": "https://nbatopshot.com"}}, {"chainId": 545, "address": "0x58b60c5240d3f39b", "contractName": "TuneGO", "path": {"storage": "/storage/TuneGOCollection", "public": "/public/TuneGOCollection"}, "flowAddress": "0x58b60c5240d3f39b", "name": "TuneKitties", "description": "Deep in the clubs and streets of the metaverse TuneKitties prowl with the purrfect mix of sound and style. Limited edition NFTs by TuneGO.", "logoURI": "https://tunegonft.com/assets/images/collections-page/tunekitties.png", "bannerURI": "https://tunegonft.com/assets/images/collections-page/tunekitties.png", "tags": [], "extensions": {"website": "https://www.tunegonft.com/collection-details/98434c03-17e3-4e9a-9f6b-3b786a4fad6e"}}, {"chainId": 545, "address": "0x58b60c5240d3f39b", "contractName": "TuneGONFTV5", "path": {"storage": "/storage/tunegoNFTCollection013", "public": "/public/tunegoNFTCollection013"}, "flowAddress": "0x58b60c5240d3f39b", "name": "TuneGO NFT", "description": "Unique music collectibles from the TuneGO Community", "logoURI": "https://www.tunegonft.com/assets/images/tunego-beta-logo.png", "bannerURI": "https://www.tunegonft.com/assets/images/tunego-beta-logo.png", "tags": [], "extensions": {"instagram": "https://www.instagram.com/tunego", "tiktok": "https://www.tiktok.com/@tunegoadmin?lang=en", "discord": "https://discord.gg/nsGnsRbMke", "facebook": "https://www.facebook.com/tunego", "twitter": "https://twitter.com/TuneGONFTV5", "website": "https://www.tunegonft.com/"}}, {"chainId": 545, "address": "0x04625c28593d9408", "contractName": "ufcInt_NFT", "path": {"storage": "/storage/ufcInt_NFTCollection", "public": "/public/ufcInt_NFTCollection"}, "flowAddress": "0x04625c28593d9408", "name": "ufcInt shop", "description": "This is a ufcInt testnet shop", "logoURI": "https://media-local.gigantik.io/ufcint/square.png", "bannerURI": "https://media-local.gigantik.io/ufcint/banner.png", "tags": [], "extensions": {"twitter": "https://twitter.com/UFCStrikeNFT", "instagram": "https://instagram.com/ufcstrike", "discord": "https://discord.gg/UFCStrike", "website": "https://ufcInt.shops-staging.gigantik.io/"}}, {"chainId": 545, "address": "0x1c5033ad60821c97", "contractName": "Wearables", "path": {"storage": "/storage/wearables", "public": "/public/wearables"}, "flowAddress": "0x1c5033ad60821c97", "name": "Wearables", "description": "Doodles 2 lets anyone create a uniquely personalized and endlessly customizable character in a one-of-a-kind style. Wearables and other collectibles can easily be bought, traded, or sold. Doodles 2 will also incorporate collaborative releases with top brands in fashion, music, sports, gaming, and more.\n\nDoodles 2 Private Beta, which will offer first access to the Doodles character creator tools, will launch later in 2022. Doodles 2 Private Beta will only be available to Beta Pass holders.", "logoURI": "https://ipfs.io/ipfs/QmVpAiutpnzp3zR4q2cUedMxsZd8h5HDeyxs9x3HibsnJb", "bannerURI": "https://ipfs.io/ipfs/QmWEsThoSdJHNVcwexYuSucR4MEGhkJEH6NCzdTV71y6GN", "tags": [], "extensions": {"discord": "https://discord.gg/doodles", "twitter": "https://twitter.com/doodles", "website": "https://doodles.app"}}, {"chainId": 545, "address": "0x7dc7430a06f38af3", "contractName": "ZeedzINO", "path": {"storage": "/storage/ZeedzINOCollection", "public": "/public/ZeedzINOCollection"}, "flowAddress": "0x7dc7430a06f38af3", "name": "Zeedz", "description": "Zeedz is the first play-for-purpose game where players reduce global carbon emissions by collecting plant-inspired creatures: Zeedles. They live as NFTs on an eco-friendly blockchain (Flow) and grow with the real-world weather.", "logoURI": "https://play.zeedz.io/logo-zeedz.svg", "bannerURI": "https://play.zeedz.io/background-zeedz.jpg", "tags": [], "extensions": {"youtube": "https://www.youtube.com/c/zeedz_official", "twitter": "https://twitter.com/zeedz_official", "discord": "http://discord.com/invite/zeedz", "instagram": "https://www.instagram.com/zeedz_official/", "linkedin": "https://www.linkedin.com/company/zeedz", "medium": "https://blog.zeedz.io/", "website": "https://play.zeedz.io"}}], "totalAmount": 28, "filterType": "ALL", "timestamp": "2024-10-02T02:10:29.921Z", "tags": {"stablecoin": {"name": "stablecoin", "description": "Tokens that are fixed to an external asset, e.g. the US dollar"}, "ethereum": {"name": "ethereum", "description": "Asset bridged from ethereum"}, "wrapped-celer": {"name": "wrapped-celer", "description": "Asset wrapped using celer bridge"}, "utility-token": {"name": "utility-token", "description": "Tokens that are designed to be spent within a certain blockchain ecosystem"}, "governance-token": {"name": "governance-token", "description": "Tokens that are designed to be use in community governance and maintenance"}, "memecoin": {"name": "memecoin", "description": "Tokens that are created for fun and meme"}}, "version": {"major": 1, "minor": 0, "patch": 3}}