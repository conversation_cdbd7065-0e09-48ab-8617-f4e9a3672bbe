import React from 'react';

export const BackupIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.7193 15.9515L14.9999 14.2312V19.9209C14.9999 20.1198 14.9209 20.3106 14.7802 20.4512C14.6396 20.5919 14.4488 20.6709 14.2499 20.6709C14.051 20.6709 13.8602 20.5919 13.7196 20.4512C13.5789 20.3106 13.4999 20.1198 13.4999 19.9209V14.2312L11.7805 15.9515C11.7109 16.0212 11.6281 16.0765 11.5371 16.1142C11.446 16.1519 11.3485 16.1713 11.2499 16.1713C11.1514 16.1713 11.0538 16.1519 10.9627 16.1142C10.8717 16.0765 10.789 16.0212 10.7193 15.9515C10.6496 15.8818 10.5943 15.7991 10.5566 15.7081C10.5189 15.617 10.4995 15.5194 10.4995 15.4209C10.4995 15.3224 10.5189 15.2248 10.5566 15.1337C10.5943 15.0427 10.6496 14.96 10.7193 14.8903L13.7193 11.8903C13.7889 11.8205 13.8717 11.7652 13.9627 11.7275C14.0538 11.6897 14.1514 11.6703 14.2499 11.6703C14.3485 11.6703 14.4461 11.6897 14.5371 11.7275C14.6282 11.7652 14.7109 11.8205 14.7805 11.8903L17.7805 14.8903C17.8502 14.96 17.9055 15.0427 17.9432 15.1337C17.9809 15.2248 18.0003 15.3224 18.0003 15.4209C18.0003 15.5194 17.9809 15.617 17.9432 15.7081C17.9055 15.7991 17.8502 15.8818 17.7805 15.9515C17.7109 16.0212 17.6281 16.0765 17.5371 16.1142C17.446 16.1519 17.3485 16.1713 17.2499 16.1713C17.1514 16.1713 17.0538 16.1519 16.9627 16.1142C16.8717 16.0765 16.789 16.0212 16.7193 15.9515ZM14.9999 4.1709C13.4676 4.17205 11.9659 4.59954 10.6627 5.40553C9.3595 6.21151 8.30628 7.36421 7.62085 8.73465C6.80478 8.61473 5.97275 8.66436 5.17672 8.88044C4.38069 9.09652 3.63776 9.47442 2.99434 9.99052C2.35091 10.5066 1.82081 11.1498 1.43714 11.88C1.05347 12.6102 0.824469 13.4116 0.764444 14.2343C0.704419 15.0569 0.814657 15.8831 1.08827 16.6612C1.36189 17.4394 1.79301 18.1527 2.3547 18.7568C2.91639 19.3608 3.5966 19.8425 4.35283 20.1719C5.10906 20.5012 5.92508 20.6711 6.74991 20.6709H10.4999C10.6988 20.6709 10.8896 20.5919 11.0302 20.4512C11.1709 20.3106 11.2499 20.1198 11.2499 19.9209C11.2499 19.722 11.1709 19.5312 11.0302 19.3906C10.8896 19.2499 10.6988 19.1709 10.4999 19.1709H6.74991C5.55644 19.1709 4.41185 18.6968 3.56793 17.8529C2.72402 17.009 2.24991 15.8644 2.24991 14.6709C2.24991 13.4774 2.72402 12.3328 3.56793 11.4889C4.41185 10.645 5.55644 10.1709 6.74991 10.1709C6.85304 10.1709 6.95616 10.1709 7.05835 10.1821C6.85333 10.9107 6.74955 11.664 6.74991 12.4209C6.74991 12.6198 6.82893 12.8106 6.96958 12.9512C7.11024 13.0919 7.301 13.1709 7.49991 13.1709C7.69883 13.1709 7.88959 13.0919 8.03024 12.9512C8.1709 12.8106 8.24991 12.6198 8.24991 12.4209C8.2501 11.204 8.57926 10.0098 9.20256 8.96459C9.82586 7.91942 10.7201 7.06219 11.7907 6.48362C12.8613 5.90505 14.0683 5.62666 15.2842 5.67791C16.5 5.72916 17.6794 6.10814 18.6975 6.77475C19.7156 7.44135 20.5345 8.37079 21.0676 9.4647C21.6008 10.5586 21.8283 11.7763 21.7261 12.9889C21.6238 14.2015 21.1957 15.364 20.487 16.3532C19.7782 17.3424 18.8152 18.1216 17.6999 18.6084C17.5415 18.6771 17.4117 18.7982 17.3323 18.9515C17.2529 19.1047 17.2288 19.2807 17.264 19.4497C17.2993 19.6187 17.3917 19.7703 17.5258 19.879C17.6598 19.9878 17.8273 20.0469 17.9999 20.0465C18.1033 20.0464 18.2055 20.0247 18.2999 19.9828C20.0374 19.2249 21.4609 17.8917 22.331 16.2076C23.2011 14.5234 23.4646 12.591 23.0773 10.7354C22.6901 8.87978 21.6756 7.21412 20.2045 6.01858C18.7334 4.82304 16.8955 4.17057 14.9999 4.1709Z"
        fill={color}
      />
    </svg>
  );
};
