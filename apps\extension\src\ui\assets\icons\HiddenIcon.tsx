import React from 'react';

const HiddenIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_3010_10899)">
      <path
        d="M10.3375 4.74C11.0258 4.57888 11.7306 4.49834 12.4375 4.5C19.4375 4.5 23.4375 12.5 23.4375 12.5C22.8305 13.6356 22.1066 14.7047 21.2775 15.69M14.5575 14.62C14.2829 14.9147 13.9516 15.1512 13.5837 15.3151C13.2157 15.4791 12.8184 15.5673 12.4156 15.5744C12.0128 15.5815 11.6127 15.5074 11.2391 15.3565C10.8656 15.2056 10.5262 14.981 10.2413 14.6962C9.95647 14.4113 9.73189 14.0719 9.58101 13.6984C9.43012 13.3248 9.35603 12.9247 9.36313 12.5219C9.37024 12.1191 9.45841 11.7218 9.62238 11.3538C9.78634 10.9859 10.0228 10.6546 10.3175 10.38M1.4375 1.5L23.4375 23.5M18.3775 18.44C16.6681 19.743 14.5866 20.4649 12.4375 20.5C5.4375 20.5 1.4375 12.5 1.4375 12.5C2.68139 10.1819 4.40664 8.15661 6.4975 6.56L18.3775 18.44Z"
        stroke="white"
        strokeOpacity="0.501961"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_3010_10899">
        <rect width="24" height="24" fill="white" transform="translate(0.4375 0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export default HiddenIcon;
