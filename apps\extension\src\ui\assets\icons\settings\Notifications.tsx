import React from 'react';

export const NotificationsIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.7936 17.4153C20.2733 16.519 19.4999 13.9831 19.4999 10.6709C19.4999 8.68177 18.7097 6.77412 17.3032 5.3676C15.8967 3.96107 13.989 3.1709 11.9999 3.1709C10.0108 3.1709 8.10311 3.96107 6.69659 5.3676C5.29007 6.77412 4.49989 8.68177 4.49989 10.6709C4.49989 13.984 3.72551 16.519 3.2052 17.4153C3.07233 17.6431 3.00189 17.902 3.00099 18.1658C3.00008 18.4295 3.06874 18.6889 3.20005 18.9176C3.33135 19.1464 3.52065 19.3365 3.74886 19.4687C3.97708 19.601 4.23613 19.6707 4.49989 19.6709H8.32583C8.49886 20.5176 8.95904 21.2786 9.62852 21.8251C10.298 22.3716 11.1357 22.6701 11.9999 22.6701C12.8641 22.6701 13.7018 22.3716 14.3713 21.8251C15.0407 21.2786 15.5009 20.5176 15.674 19.6709H19.4999C19.7636 19.6705 20.0225 19.6007 20.2506 19.4684C20.4787 19.336 20.6678 19.1459 20.799 18.9172C20.9302 18.6885 20.9988 18.4292 20.9979 18.1655C20.9969 17.9018 20.9265 17.6431 20.7936 17.4153ZM11.9999 21.1709C11.5347 21.1708 11.081 21.0264 10.7013 20.7578C10.3215 20.4892 10.0343 20.1095 9.87927 19.6709H14.1205C13.9655 20.1095 13.6783 20.4892 13.2985 20.7578C12.9188 21.0264 12.4651 21.1708 11.9999 21.1709ZM4.49989 18.1709C5.22177 16.9296 5.99989 14.0534 5.99989 10.6709C5.99989 9.0796 6.63203 7.55348 7.75725 6.42826C8.88247 5.30304 10.4086 4.6709 11.9999 4.6709C13.5912 4.6709 15.1173 5.30304 16.2425 6.42826C17.3678 7.55348 17.9999 9.0796 17.9999 10.6709C17.9999 14.0506 18.7761 16.9268 19.4999 18.1709H4.49989Z"
        fill={color}
      />
    </svg>
  );
};
