name: Continuous Integration

on:
  push:
    paths:
      - "packages/**"
    branches: [main, dev]
  pull_request:
    paths:
      - "packages/**"
    branches: [main, dev]

jobs:
  ci:
    runs-on: ubuntu-latest
    name: CI - Build, Test, and Security Audit

    steps:
      - uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build all packages
        run: pnpm build

      - name: Run tests
        run: pnpm test
        continue-on-error: true

      - name: Type check packages
        run: pnpm -r --filter='./packages/*' typecheck

      - name: Run security audit
        run: pnpm audit --audit-level moderate
        continue-on-error: true
