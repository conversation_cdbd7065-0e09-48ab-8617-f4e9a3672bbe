import React from 'react';

export const ThemeIcon = ({
  color = '#00EF8B',
  width = 29,
  height = 28,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.56157 1.4209C8.1433 2.14346 8.57366 3.0063 8.79295 3.97182C9.56301 7.36235 7.4387 10.7352 4.04817 11.5052C3.35742 11.6621 2.66741 11.6989 2 11.6292C3.4605 13.4331 5.86774 14.3559 8.28399 13.8072C11.6707 13.038 13.7925 9.669 13.0234 6.28233C12.4122 3.59156 10.1599 1.69919 7.56157 1.4209Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M25.3911 9.88888C25.8071 9.47288 25.8071 8.79842 25.3911 8.38242C24.9751 7.96642 24.3006 7.96642 23.8846 8.38242L22.3782 9.88888C21.9622 10.3049 21.9622 10.9793 22.3782 11.3953C22.7942 11.8113 23.4687 11.8113 23.8846 11.3953L25.3911 9.88888Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M12.5862 19.6809C14.6662 21.7609 18.0385 21.7609 20.1185 19.6809C22.1985 17.6009 22.1985 14.2285 20.1185 12.1486L12.5862 19.6809Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M23.1314 24.2005C23.5474 24.6165 24.2219 24.6165 24.6378 24.2005C25.0538 23.7845 25.0538 23.1101 24.6378 22.6941L23.1314 21.1876C22.7154 20.7716 22.0409 20.7716 21.6249 21.1876C21.2089 21.6036 21.2089 22.2781 21.6249 22.6941L23.1314 24.2005Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M11.8329 21.9408C12.2489 22.3568 12.2489 23.0313 11.8329 23.4473L10.3265 24.9538C9.91046 25.3698 9.23599 25.3698 8.81999 24.9538C8.404 24.5378 8.404 23.8633 8.81999 23.4473L10.3265 21.9408C10.7425 21.5248 11.4169 21.5248 11.8329 21.9408Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M16.7936 22.8232C17.3819 22.8232 17.8588 23.3001 17.8588 23.8884V26.0189C17.8588 26.6072 17.3819 27.0841 16.7936 27.0841C16.2053 27.0841 15.7283 26.6072 15.7283 26.0189V23.8884C15.7283 23.3001 16.2053 22.8232 16.7936 22.8232Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M26.5856 17.2919C27.1739 17.2919 27.6508 16.815 27.6508 16.2266C27.6508 15.6383 27.1739 15.1614 26.5856 15.1614H24.4551C23.8668 15.1614 23.3899 15.6383 23.3899 16.2266C23.3899 16.815 23.8668 17.2919 24.4551 17.2919H26.5856Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
      <path
        d="M5.46378 21.8725C5.24523 22.0911 5.24523 22.4454 5.46378 22.6639C5.68233 22.8825 6.03667 22.8825 6.25522 22.6639L22.2527 6.66649C22.4712 6.44794 22.4712 6.0936 22.2527 5.87506C22.0341 5.65651 21.6798 5.65651 21.4612 5.87506L5.46378 21.8725Z"
        fill={color}
        stroke={color}
        strokeLinecap="round"
      />
    </svg>
  );
};
