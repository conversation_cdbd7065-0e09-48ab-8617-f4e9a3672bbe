import React from 'react';

export const SecurityIcon = ({
  color = '#00EF8B',
  width = 24,
  height = 25,
}: {
  color?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2.6709C10.0716 2.6709 8.18657 3.24273 6.58319 4.31407C4.97982 5.38541 3.73013 6.90815 2.99218 8.68973C2.25422 10.4713 2.06114 12.4317 2.43735 14.323C2.81355 16.2143 3.74215 17.9516 5.10571 19.3152C6.46928 20.6788 8.20656 21.6073 10.0979 21.9836C11.9892 22.3598 13.9496 22.1667 15.7312 21.4287C17.5127 20.6908 19.0355 19.4411 20.1068 17.8377C21.1782 16.2343 21.75 14.3493 21.75 12.4209C21.7473 9.83588 20.7192 7.35751 18.8913 5.52962C17.0634 3.70173 14.585 2.67363 12 2.6709ZM12 20.6709C10.3683 20.6709 8.77326 20.187 7.41655 19.2805C6.05984 18.374 5.00242 17.0855 4.378 15.578C3.75358 14.0705 3.5902 12.4117 3.90853 10.8114C4.22685 9.21106 5.01259 7.74105 6.16637 6.58727C7.32016 5.43348 8.79017 4.64775 10.3905 4.32942C11.9909 4.01109 13.6497 4.17447 15.1571 4.79889C16.6646 5.42332 17.9531 6.48074 18.8596 7.83744C19.7661 9.19415 20.25 10.7892 20.25 12.4209C20.2475 14.6082 19.3775 16.7052 17.8309 18.2518C16.2843 19.7984 14.1873 20.6684 12 20.6709ZM15.75 10.9209C15.7501 10.2915 15.5919 9.67222 15.2898 9.12008C14.9876 8.56794 14.5514 8.10072 14.0213 7.76147C13.4912 7.42223 12.8842 7.22187 12.2563 7.17886C11.6284 7.13585 10.9997 7.25157 10.4283 7.51537C9.85684 7.77916 9.36099 8.18254 8.98642 8.68832C8.61185 9.19411 8.3706 9.78604 8.28492 10.4096C8.19923 11.0331 8.27186 11.6682 8.49612 12.2562C8.72037 12.8443 9.08903 13.3665 9.56813 13.7746L8.31375 16.6181C8.26337 16.7323 8.24227 16.8572 8.25237 16.9816C8.26247 17.106 8.30345 17.2259 8.3716 17.3304C8.43975 17.435 8.53291 17.5209 8.64264 17.5803C8.75237 17.6398 8.8752 17.6709 9 17.6709H15C15.1248 17.6709 15.2476 17.6398 15.3574 17.5803C15.4671 17.5209 15.5603 17.435 15.6284 17.3304C15.6966 17.2259 15.7375 17.106 15.7476 16.9816C15.7577 16.8572 15.7366 16.7323 15.6863 16.6181L14.4319 13.7746C14.8443 13.4221 15.1757 12.9846 15.4032 12.492C15.6307 11.9994 15.749 11.4635 15.75 10.9209ZM12.8138 13.8271L13.845 16.1756H10.1503L11.1816 13.8271C11.258 13.658 11.269 13.4664 11.2123 13.2896C11.1557 13.1128 11.0355 12.9633 10.875 12.87C10.4461 12.6223 10.1108 12.24 9.92128 11.7824C9.73173 11.3248 9.69848 10.8175 9.82667 10.3391C9.95486 9.86064 10.2373 9.43788 10.6303 9.13636C11.0232 8.83484 11.5047 8.6714 12 8.6714C12.4953 8.6714 12.9768 8.83484 13.3697 9.13636C13.7627 9.43788 14.0451 9.86064 14.1733 10.3391C14.3015 10.8175 14.2683 11.3248 14.0787 11.7824C13.8892 12.24 13.5539 12.6223 13.125 12.87C12.9637 12.9625 12.8425 13.1117 12.785 13.2886C12.7275 13.4655 12.7377 13.6574 12.8138 13.8271Z"
        fill={color}
      />
    </svg>
  );
};
