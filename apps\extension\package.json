{"name": "frw-extension", "version": "2.9.3", "description": "Digital wallet created for everyone.", "type": "module", "scripts": {"prepare:dev": "pnpx tsx ./build/prepareManifest.ts dev", "prepare:pro": "pnpx tsx ./build/prepareManifest.ts pro", "clean": "<PERSON><PERSON><PERSON> dist", "build:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm run prepare:dev && pnpm run clean && node build/webpack-cli-wrapper.cjs --progress --env config=dev", "build:dev-ci": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm run prepare:dev && pnpm run clean && node build/webpack-cli-wrapper.cjs --env config=dev --no-watch", "build:ci": "pnpm run prepare:pro && pnpm run clean && node build/webpack-cli-wrapper.cjs --env config=pro", "build:pro": "pnpm run prepare:pro && pnpm run clean && node build/webpack-cli-wrapper.cjs --progress --env config=pro", "build:test": "pnpm run prepare:dev && pnpm run clean && node build/webpack-cli-wrapper.cjs --progress --env config=none", "lint:fix": "eslint --fix --cache .", "lint": "eslint --cache .", "format:fix": "prettier --write .", "format": "prettier .", "icon": "npx iconfont-h5", "test": "vitest", "test:run": "vitest --run", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:ui:debug": "playwright test --ui --debug", "test:unit": "vitest", "test:unit:coverage": "vitest --coverage", "pub": "pnpx tsx build/release.ts", "preinstall": "npx only-allow pnpm", "analyze:imports": "pnpx tsx build/analyze-imports.ts", "analyze:dependencies": "pnpx tsx build/analyze-dependencies.ts", "analyze:fetch": "./build/fetch-project-data.sh", "analyze:project": "pnpx tsx build/analyze-project.ts", "analyze:priority": "pnpm analyze:fetch && pnpm analyze:project", "emulate:testnet": "flow emulator --chain-id testnet --rpc-host access.devnet.nodes.onflow.org:9000", "emulate:mainnet": "flow emulator --chain-id mainnet --rpc-host access.mainnet.nodes.onflow.org:9000", "gh:login": "gh auth login -s project && gh auth status", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build:zip": "pnpm build && node scripts/build-zip.js"}, "dependencies": {"@coinbase/cbpay-js": "^1.10.0", "@debank/common": "^0.3.60", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@luca/cases": "jsr:^1.0.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@noble/secp256k1": "^1.7.2", "@onflow/fcl": "^1.19.0", "@onflow/frw-cadence": "workspace:*", "@reown/walletkit": "^1.2.8", "@trustwallet/wallet-core": "^4.3.6", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@walletconnect/core": "^2.21.4", "@walletconnect/jsonrpc-utils": "^1.0.8", "@walletconnect/sign-client": "^2.21.4", "@walletconnect/types": "^2.21.4", "@walletconnect/utils": "^2.21.4", "aes-js": "^3.1.2", "assert": "^2.1.0", "bignumber.js": "^9.3.0", "bip39": "^3.1.0", "browser-passworder": "^2.0.3", "buffer": "^6.0.3", "compare-versions": "^4.1.4", "cross-fetch": "^4.1.0", "crypto-browserify": "^3.12.1", "dayjs": "^1.11.13", "debounce": "^2.2.0", "dedent": "^0.7.0", "eth-rpc-errors": "^4.0.3", "ethereum-hdwallet": "^0.0.26", "ethereumjs-util": "^7.1.5", "ethers": "^6.15.0", "events": "^3.3.0", "file-saver": "^2.0.5", "firebase": "^10.14.1", "hdkey": "0.8.0", "history": "^4.10.1", "koa-compose": "^4.1.0", "lodash": "^4.17.21", "loglevel": "^1.9.2", "lru-cache": "^11.1.0", "nanoid": "^3.3.11", "process": "^0.11.10", "qr-code-styling": "^1.9.2", "qr-scanner": "^1.4.2", "raw-loader": "^4.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-highlight": "^0.15.0", "react-hook-form": "^7.60.0", "react-infinite-scroll-component": "^6.1.0", "react-qrcode-logo": "^3.0.0", "react-router": "^7.6.3", "recharts": "^2.15.4", "reflect-metadata": "^0.1.14", "rlp": "^3.0.0", "secp256k1": "^4.0.4", "sha3": "^2.1.4", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "ts-toolbelt": "^9.6.0", "tsparticles": "~3.6.0", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "vm-browserify": "^1.1.2", "web3": "^4.16.0", "web3-utils": "^4.3.3", "webextension-polyfill": "^0.12.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@eslint/js": "^9.30.1", "@metamask/eth-sig-util": "^8.2.0", "@playwright/test": "^1.53.2", "@storybook/addon-docs": "^9.0.16", "@storybook/addon-onboarding": "^9.0.16", "@storybook/addon-themes": "^9.0.16", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/react-webpack5": "^9.0.16", "@svgr/webpack": "^5.5.0", "@types/chrome": "^0.0.281", "@types/koa-compose": "^3.2.8", "@types/lodash": "^4.17.20", "@types/node": "^22.16.2", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitest/coverage-v8": "3.2.4", "@welldone-software/why-did-you-render": "^8.0.3", "autoprefixer": "^10.4.21", "chromatic": "^12.2.0", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "dotenv": "^16.6.1", "dotenv-webpack": "^8.1.1", "enquirer": "^2.4.1", "eslint": "^9.30.1", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^9.0.16", "eslint-plugin-unused-imports": "^4.1.4", "file-loader": "^6.2.0", "globals": "^15.15.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "i18next": "^24.2.3", "jszip": "^3.10.1", "lint-staged": "^16.1.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "pinst": "^3.0.0", "postcss": "^8.5.6", "postcss-custom-properties": "^14.0.6", "postcss-import": "^16.1.1", "postcss-loader": "^5.3.0", "postcss-nested": "^7.0.2", "prettier": "^3.6.2", "react-devtools": "^6.1.5", "react-i18next": "^15.6.0", "rimraf": "^6.0.1", "shelljs": "^0.8.5", "sinon-chrome": "^3.0.1", "storybook": "^9.0.16", "storybook-addon-remix-react-router": "^5.0.0", "style-loader": "^4.0.0", "table": "^6.9.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "typescript-transform-paths": "^3.5.5", "url-loader": "^4.1.1", "vitest": "^3.2.4", "webpack": "^5.99.9", "webpack-cli": "^4.10.0", "webpack-merge": "^5.10.0", "zip-dir": "^2.0.0"}, "keywords": [], "author": "", "license": "LGPL-3.0-or-later", "engines": {"node": ">=22.11.0", "pnpm": ">=9"}, "packageManager": "pnpm@10.12.1"}